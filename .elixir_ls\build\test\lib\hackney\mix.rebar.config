{erl_opts,[debug_info,
           {platform_define,"R14",no_callback_support},
           {platform_define,"^[0-9]+",namespaced_types},
           {platform_define,"^R",no_proxy_sni_support},
           {platform_define,"^(1|(20))",no_customize_hostname_check},
           {platform_define,"^20.3",buggy_chacha_ciphers}]}.
{erl_first_files,["src/hackney_ssl_certificate.erl"]}.
{xref_checks,[undefined_function_calls]}.
{cover_enabled,true}.
{eunit_opts,[verbose]}.
{post_hooks,[{clean,"rm -rf *~ */*~ */*.xfm test/*.beam"}]}.
{deps,[{idna,"~>6.1.0"},
       {mimerl,"~>1.4"},
       {certifi,"~>2.15.0"},
       {metrics,"~>1.0.0"},
       {parse_trans,"3.4.1"},
       {ssl_verify_fun,"~>1.1.0"},
       {unicode_util_compat,"~>0.7.0"}]}.
{profiles,[{test,[{deps,[{cowboy,"1.1.2"},{jsone,"1.4.7"}]}]}]}.
{hex,[{doc,#{provider => ex_doc}}]}.
{ex_doc,[{extras,[{"README.md",#{title => "Overview"}},
                  {"NEWS.md",#{title => "Changelog"}},
                  {"LICENSE",#{title => "License"}},
                  {"NOTICE",#{title => "Notice"}}]},
         {main,"README.md"},
         {source_url,"https://github.com/benoitc/hackney"},
         {source_ref,<<"master">>}]}.
{dialyzer,[{warnings,[no_return,unmatched_returns,error_handling]},
           {base_plt_apps,[erts,stdlib,kernel,crypto,runtime_tools]},
           {plt_apps,top_level_deps},
           {plt_extra_apps,[]},
           {plt_location,local},
           {plt_prefix,"hackney"},
           {base_plt_location,"."},
           {base_plt_prefix,"hackney"}]}.
{overrides,[]}.
