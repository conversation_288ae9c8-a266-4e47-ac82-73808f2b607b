# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: nl\n"

#: lib/l10n/translator.ex:261
msgid "%{count} day"
msgid_plural "%{count} days"
msgstr[0] "%{count} dag"
msgstr[1] "%{count} dagen"

#: lib/l10n/translator.ex:260
msgid "%{count} hour"
msgid_plural "%{count} hours"
msgstr[0] "%{count} uur"
msgstr[1] "%{count} uren"

#: lib/l10n/translator.ex:256
msgid "%{count} microsecond"
msgid_plural "%{count} microseconds"
msgstr[0] "%{count} microseconde"
msgstr[1] "%{count} microseconden"

#: lib/l10n/translator.ex:257
msgid "%{count} millisecond"
msgid_plural "%{count} milliseconds"
msgstr[0] "%{count} milliseconde"
msgstr[1] "%{count} milliseconden"

#: lib/l10n/translator.ex:259
msgid "%{count} minute"
msgid_plural "%{count} minutes"
msgstr[0] "%{count} minuut"
msgstr[1] "%{count} minuten"

#: lib/l10n/translator.ex:263
msgid "%{count} month"
msgid_plural "%{count} months"
msgstr[0] "%{count} maand"
msgstr[1] "%{count} maanden"

#: lib/l10n/translator.ex:255
msgid "%{count} nanosecond"
msgid_plural "%{count} nanoseconds"
msgstr[0] "%{count} nanoseconde"
msgstr[1] "%{count} nanoseconden"

#: lib/l10n/translator.ex:258
msgid "%{count} second"
msgid_plural "%{count} seconds"
msgstr[0] "%{count} seconde"
msgstr[1] "%{count} seconden"

#: lib/l10n/translator.ex:262
msgid "%{count} week"
msgid_plural "%{count} weeks"
msgstr[0] "%{count} week"
msgstr[1] "%{count} weken"

#: lib/l10n/translator.ex:264
msgid "%{count} year"
msgid_plural "%{count} years"
msgstr[0] "%{count} jaar"
msgstr[1] "%{count} jaren"
