## `msgid`s in this file come from POT (.pot) files.
##
## Do not add, change, or remove `msgid`s manually here as
## they're tied to the ones in the corresponding POT file
## (with the same domain).
##
## Use `mix gettext.extract --merge` or `mix gettext.merge`
## to merge POT files into PO files.
msgid ""
msgstr ""
"Language: uk\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "пт"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "п'ятниця"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "пн"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "понеділок"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "сб"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "субота"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "нд"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "неділя"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "чт"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "четвер"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "вт"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "вівторок"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "ср"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "середа"
