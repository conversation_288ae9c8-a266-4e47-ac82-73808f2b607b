defmodule Reconciliation do
  @moduledoc """
  Reconciliation keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.
  """

  # Delegate reconciliation functions to the Reconciliation context
  defdelegate list_reconciliation_runs(user_id), to: Reconciliation.Reconciliation
  defdelegate get_reconciliation_run!(id), to: Reconciliation.Reconciliation
  defdelegate get_user_reconciliation_run(id, user_id), to: Reconciliation.Reconciliation
  defdelegate create_reconciliation_run(attrs), to: Reconciliation.Reconciliation
  defdelegate update_reconciliation_run(run, attrs), to: Reconciliation.Reconciliation
  defdelegate mark_reconciliation_failed(run, error), to: Reconciliation.Reconciliation
  defdelegate delete_reconciliation_run(run), to: Reconciliation.Reconciliation

  defdelegate create_uploaded_file(attrs), to: Reconciliation.Reconciliation
  defdelegate update_uploaded_file(file, attrs), to: Reconciliation.Reconciliation
  defdelegate mark_file_failed(file, errors), to: Reconciliation.Reconciliation
  defdelegate get_uploaded_files(run_id), to: Reconciliation.Reconciliation

  defdelegate create_transaction(attrs), to: Reconciliation.Reconciliation
  defdelegate create_transactions(attrs_list), to: Reconciliation.Reconciliation
  defdelegate update_transaction_match_status(transaction, attrs), to: Reconciliation.Reconciliation
  defdelegate get_transactions(run_id), to: Reconciliation.Reconciliation
  defdelegate get_unmatched_transactions(run_id), to: Reconciliation.Reconciliation

  defdelegate create_transaction_match(attrs), to: Reconciliation.Reconciliation
  defdelegate get_transaction_matches(run_id), to: Reconciliation.Reconciliation
  defdelegate verify_transaction_match(match, verified), to: Reconciliation.Reconciliation

  defdelegate get_or_create_settings(user_id), to: Reconciliation.Reconciliation
  defdelegate update_settings(settings, attrs), to: Reconciliation.Reconciliation

  defdelegate get_reconciliation_stats(run_id), to: Reconciliation.Reconciliation
  defdelegate calculate_reconciliation_stats(run_id), to: Reconciliation.Reconciliation
end
