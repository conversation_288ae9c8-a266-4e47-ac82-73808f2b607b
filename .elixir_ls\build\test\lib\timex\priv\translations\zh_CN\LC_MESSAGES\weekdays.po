# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: zh_CN\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "星期五"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "星期五"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "星期一"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "星期一"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "星期六"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "星期六"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "星期日"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "星期日"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "星期四"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "星期四"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "星期二"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "星期二"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "星期三"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "星期三"
