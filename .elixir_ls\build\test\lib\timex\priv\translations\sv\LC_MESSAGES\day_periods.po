## `msgid`s here are often extracted from source code.
## Add new translations manually only if they're dynamic
## translations that can't be statically extracted.
##
## Run `mix gettext.extract` to bring this file up to
## date. Leave `msgstr`s empty as changing them here as no
## effect: edit them in PO (`.po`) files instead.
msgid ""
msgstr ""
"Language: sv\n"

#: lib/l10n/translator.ex:266
msgid "AM"
msgstr "FM"

#: lib/l10n/translator.ex:268
msgid "PM"
msgstr "EM"

#: lib/l10n/translator.ex:267
msgid "am"
msgstr "fm"

#: lib/l10n/translator.ex:269
msgid "pm"
msgstr "em"
