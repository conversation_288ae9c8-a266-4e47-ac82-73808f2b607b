## `msgid`s here are often extracted from source code.
## Add new translations manually only if they're dynamic
## translations that can't be statically extracted.
##
## Run `mix gettext.extract` to bring this file up to
## date. Leave `msgstr`s empty as changing them here as no
## effect: edit them in PO (`.po`) files instead.
msgid ""
msgstr ""
"Language: sv\n"

#: lib/l10n/translator.ex:290
msgid "Apr"
msgstr "Apr"

#: lib/l10n/translator.ex:294
msgid "Aug"
msgstr "Aug"

#: lib/l10n/translator.ex:298
msgid "Dec"
msgstr "Dec"

#: lib/l10n/translator.ex:288
msgid "Feb"
msgstr "Feb"

#: lib/l10n/translator.ex:287
msgid "Jan"
msgstr "Jan"

#: lib/l10n/translator.ex:293
msgid "Jul"
msgstr "Jul"

#: lib/l10n/translator.ex:292
msgid "Jun"
msgstr "Jun"

#: lib/l10n/translator.ex:289
msgid "Mar"
msgstr "Mar"

#: lib/l10n/translator.ex:291
msgid "May"
msgstr "Maj"

#: lib/l10n/translator.ex:297
msgid "Nov"
msgstr "Nov"

#: lib/l10n/translator.ex:296
msgid "Oct"
msgstr "Okt"

#: lib/l10n/translator.ex:295
msgid "Sep"
msgstr "Sep"
