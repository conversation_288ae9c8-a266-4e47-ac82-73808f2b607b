# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: id\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=iso-8859-1\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "Jum"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "Jumat"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "Sen"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "Senin"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "Sab"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "Sabtu"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "Min"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "Minggu"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "Kam"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "Kamis"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "Sel"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "Selasa"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "Rab"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "Rabu"
