defmodule Reconciliation.Reconciliation do
  @moduledoc """
  The Reconciliation context.
  """

  import Ecto.Query, warn: false
  alias Reconciliation.Repo

  alias Reconciliation.{
    ReconciliationRun,
    UploadedFile,
    Transaction,
    TransactionMatch,
    ReconciliationSettings
  }

  ## Reconciliation Runs

  @doc """
  Returns the list of reconciliation runs for a user.
  """
  def list_reconciliation_runs(user_id) do
    ReconciliationRun
    |> where([r], r.user_id == ^user_id)
    |> order_by([r], desc: r.inserted_at)
    |> preload([:uploaded_files, :user])
    |> Repo.all()
  end

  @doc """
  Gets a single reconciliation run.
  """
  def get_reconciliation_run!(id) do
    ReconciliationRun
    |> preload([:uploaded_files, :transactions, :transaction_matches, :user])
    |> Repo.get!(id)
  end

  @doc """
  Gets a reconciliation run by id and user_id.
  """
  def get_user_reconciliation_run(id, user_id) do
    ReconciliationRun
    |> where([r], r.id == ^id and r.user_id == ^user_id)
    |> preload([:uploaded_files, :transactions, :transaction_matches, :user])
    |> Repo.one()
  end

  @doc """
  Creates a reconciliation run.
  """
  def create_reconciliation_run(attrs \\ %{}) do
    %ReconciliationRun{}
    |> ReconciliationRun.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a reconciliation run.
  """
  def update_reconciliation_run(%ReconciliationRun{} = reconciliation_run, attrs) do
    reconciliation_run
    |> ReconciliationRun.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates reconciliation run statistics.
  """
  def update_reconciliation_stats(%ReconciliationRun{} = reconciliation_run, attrs) do
    reconciliation_run
    |> ReconciliationRun.stats_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Marks a reconciliation run as failed with error message.
  """
  def mark_reconciliation_failed(%ReconciliationRun{} = reconciliation_run, error_message) do
    reconciliation_run
    |> ReconciliationRun.error_changeset(error_message)
    |> Repo.update()
  end

  @doc """
  Deletes a reconciliation run.
  """
  def delete_reconciliation_run(%ReconciliationRun{} = reconciliation_run) do
    Repo.delete(reconciliation_run)
  end

  ## Uploaded Files

  @doc """
  Creates an uploaded file record.
  """
  def create_uploaded_file(attrs \\ %{}) do
    %UploadedFile{}
    |> UploadedFile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an uploaded file.
  """
  def update_uploaded_file(%UploadedFile{} = uploaded_file, attrs) do
    uploaded_file
    |> UploadedFile.processing_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Marks an uploaded file as failed with errors.
  """
  def mark_file_failed(%UploadedFile{} = uploaded_file, errors) do
    uploaded_file
    |> UploadedFile.error_changeset(errors)
    |> Repo.update()
  end

  @doc """
  Gets uploaded files for a reconciliation run.
  """
  def get_uploaded_files(reconciliation_run_id) do
    UploadedFile
    |> where([f], f.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([f], asc: f.file_type)
    |> Repo.all()
  end

  ## Transactions

  @doc """
  Creates a transaction.
  """
  def create_transaction(attrs \\ %{}) do
    %Transaction{}
    |> Transaction.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates multiple transactions in a batch.
  """
  def create_transactions(transactions_attrs) do
    Repo.insert_all(Transaction, transactions_attrs, returning: true)
  end

  @doc """
  Updates a transaction.
  """
  def update_transaction(%Transaction{} = transaction, attrs) do
    transaction
    |> Transaction.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates transaction match status.
  """
  def update_transaction_match_status(%Transaction{} = transaction, attrs) do
    transaction
    |> Transaction.match_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Gets transactions for a reconciliation run.
  """
  def get_transactions(reconciliation_run_id) do
    Transaction
    |> where([t], t.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([t], asc: t.transaction_date, asc: t.row_number)
    |> preload([:uploaded_file])
    |> Repo.all()
  end

  @doc """
  Gets transactions for a specific file.
  """
  def get_file_transactions(uploaded_file_id) do
    Transaction
    |> where([t], t.uploaded_file_id == ^uploaded_file_id)
    |> order_by([t], asc: t.row_number)
    |> Repo.all()
  end

  @doc """
  Gets unmatched transactions for a reconciliation run.
  """
  def get_unmatched_transactions(reconciliation_run_id) do
    Transaction
    |> where([t], t.reconciliation_run_id == ^reconciliation_run_id and t.is_matched == false)
    |> order_by([t], asc: t.transaction_date, asc: t.amount)
    |> preload([:uploaded_file])
    |> Repo.all()
  end

  ## Transaction Matches

  @doc """
  Creates a transaction match.
  """
  def create_transaction_match(attrs \\ %{}) do
    %TransactionMatch{}
    |> TransactionMatch.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a transaction match.
  """
  def update_transaction_match(%TransactionMatch{} = match, attrs) do
    match
    |> TransactionMatch.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Verifies a transaction match.
  """
  def verify_transaction_match(%TransactionMatch{} = match, verified \\ true) do
    match
    |> TransactionMatch.verification_changeset(verified)
    |> Repo.update()
  end

  @doc """
  Gets transaction matches for a reconciliation run.
  """
  def get_transaction_matches(reconciliation_run_id) do
    TransactionMatch
    |> where([m], m.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([m], desc: m.confidence_score)
    |> preload([:transaction_a, :transaction_b])
    |> Repo.all()
  end

  ## Reconciliation Settings

  @doc """
  Gets or creates reconciliation settings for a user.
  """
  def get_or_create_settings(user_id) do
    ReconciliationSettings.get_or_create_for_user(user_id)
  end

  @doc """
  Updates reconciliation settings.
  """
  def update_settings(%ReconciliationSettings{} = settings, attrs) do
    settings
    |> ReconciliationSettings.changeset(attrs)
    |> Repo.update()
  end

  ## Statistics and Analytics

  @doc """
  Gets reconciliation statistics for a run.
  """
  def get_reconciliation_stats(reconciliation_run_id) do
    run = get_reconciliation_run!(reconciliation_run_id)
    
    %{
      total_transactions_a: run.total_transactions_a,
      total_transactions_b: run.total_transactions_b,
      matched_count: run.matched_count,
      unmatched_a_count: run.unmatched_a_count,
      unmatched_b_count: run.unmatched_b_count,
      total_amount_a: run.total_amount_a,
      total_amount_b: run.total_amount_b,
      difference_amount: run.difference_amount,
      match_rate: run.match_rate,
      status: run.status
    }
  end

  @doc """
  Calculates and updates reconciliation run statistics.
  """
  def calculate_reconciliation_stats(reconciliation_run_id) do
    # Get all transactions for this run
    transactions = get_transactions(reconciliation_run_id)
    
    # Separate by file type
    file_a_transactions = Enum.filter(transactions, fn t -> 
      t.uploaded_file.file_type == "file_a" 
    end)
    
    file_b_transactions = Enum.filter(transactions, fn t -> 
      t.uploaded_file.file_type == "file_b" 
    end)
    
    # Calculate totals
    total_a = length(file_a_transactions)
    total_b = length(file_b_transactions)
    
    matched_count = Enum.count(transactions, &(&1.is_matched))
    unmatched_a = total_a - Enum.count(file_a_transactions, &(&1.is_matched))
    unmatched_b = total_b - Enum.count(file_b_transactions, &(&1.is_matched))
    
    # Calculate amounts
    amount_a = file_a_transactions
    |> Enum.map(&(&1.amount))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
    
    amount_b = file_b_transactions
    |> Enum.map(&(&1.amount))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
    
    difference = Decimal.sub(amount_a, amount_b) |> Decimal.abs()
    
    # Calculate match rate
    total_transactions = max(total_a, total_b)
    match_rate = ReconciliationRun.calculate_match_rate(total_transactions, matched_count)
    
    # Update the reconciliation run
    run = get_reconciliation_run!(reconciliation_run_id)
    update_reconciliation_stats(run, %{
      total_transactions_a: total_a,
      total_transactions_b: total_b,
      matched_count: matched_count,
      unmatched_a_count: unmatched_a,
      unmatched_b_count: unmatched_b,
      total_amount_a: amount_a,
      total_amount_b: amount_b,
      difference_amount: difference,
      match_rate: match_rate,
      status: "completed",
      processed_at: DateTime.utc_now()
    })
  end
end
