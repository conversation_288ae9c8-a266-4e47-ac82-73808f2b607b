# # This file is a PO Template file.
# #
# # `msgid`s here are often extracted from source code.
# # Add new translations manually only if they're dynamic
# # translations that can't be statically extracted.
# #
# # Run `mix gettext.extract` to bring this file up to
# # date. Leave `msgstr`s empty as changing them here as no
# # effect: edit them in PO (`.po`) files instead.
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.3\n"
"Last-Translator: Pont Systems <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: hu\n"

#: lib/l10n/translator.ex:261
msgid "%{count} day"
msgid_plural "%{count} days"
msgstr[0] "%{count} nap"
msgstr[1] "%{count} nap"

#: lib/l10n/translator.ex:260
msgid "%{count} hour"
msgid_plural "%{count} hours"
msgstr[0] "%{count} óra"
msgstr[1] "%{count} óra"

#: lib/l10n/translator.ex:256
msgid "%{count} microsecond"
msgid_plural "%{count} microseconds"
msgstr[0] "%{count} mikroszekundum"
msgstr[1] "%{count} mikroszekundum"

#: lib/l10n/translator.ex:257
msgid "%{count} millisecond"
msgid_plural "%{count} milliseconds"
msgstr[0] "%{count} miliszekundum"
msgstr[1] "%{count} miliszekundum"

#: lib/l10n/translator.ex:259
msgid "%{count} minute"
msgid_plural "%{count} minutes"
msgstr[0] "%{count} perc"
msgstr[1] "%{count} perc"

#: lib/l10n/translator.ex:263
msgid "%{count} month"
msgid_plural "%{count} months"
msgstr[0] "%{count} hónap"
msgstr[1] "%{count} hónap"

#: lib/l10n/translator.ex:255
msgid "%{count} nanosecond"
msgid_plural "%{count} nanoseconds"
msgstr[0] "%{count} nanoszekundum"
msgstr[1] "%{count} nanoszekundum"

#: lib/l10n/translator.ex:258
msgid "%{count} second"
msgid_plural "%{count} seconds"
msgstr[0] "%{count} másodperc"
msgstr[1] "%{count} másodperc"

#: lib/l10n/translator.ex:262
msgid "%{count} week"
msgid_plural "%{count} weeks"
msgstr[0] "%{count} hét"
msgstr[1] "%{count} hét"

#: lib/l10n/translator.ex:264
msgid "%{count} year"
msgid_plural "%{count} years"
msgstr[0] "%{count} év"
msgstr[1] "%{count} év"
