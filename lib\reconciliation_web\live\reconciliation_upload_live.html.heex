<div>
  <h1 class="text-2xl font-semibold mb-6">Upload Reconciliation File</h1>

  <.form
    for={@form}
    phx-change="validate"
    phx-submit="save"
    phx-target={@myself}
    class="space-y-4"
  >
    <div>
      <.label for={@form[:excel_file].id}>Excel/CSV File</.label>
      <.live_file_input {@form} field={:excel_file} class="mt-1" />
    </div>

    <div class="space-y-2">
      <p :for={entry <- @uploads.excel_file.entries} class="text-sm">
        <span class="font-medium"><%= entry.client_name %></span> - <%= entry.progress %>%
        <button
          type="button"
          phx-click="cancel_upload"
          phx-value-ref={entry.ref}
          phx-target={@myself}
          aria-label="cancel"
          class="ml-2 text-red-500 hover:text-red-700"
        >&times;</button>
      </p>
      <div :for={{ref, err} <- @uploads.excel_file.errors_by_ref} class="text-red-500 text-sm">
        File: <%= Phoenix.LiveView.UploadEntry.client_name(ref) %> - <%= error_to_string(err) %>
      </div>
    </div>

    <div>
      <.button type="submit" phx-disable-with="Saving..." class="bg-blue-600 hover:bg-blue-700 text-white">
        Upload and Process File
      </.button>
    </div>
  </.form>

  <%= if @uploaded_files && Enum.any?(@uploaded_files) do %>
    <div class="mt-8">
      <h2 class="text-xl font-semibold mb-2">Processed Files:</h2>
      <ul class="list-disc list-inside">
        <%= for {:ok, path} <- @uploaded_files do %>
          <li><%= path %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

</div>