# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: en\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=iso-8859-1\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"

#: lib/l10n/translator.ex:320
msgid "last month"
msgstr "last month"

#: lib/l10n/translator.ex:326
msgid "last week"
msgstr "last week"

#: lib/l10n/translator.ex:314
msgid "last year"
msgstr "last year"

#: lib/l10n/translator.ex:322
msgid "next month"
msgstr "next month"

#: lib/l10n/translator.ex:328
msgid "next week"
msgstr "next week"

#: lib/l10n/translator.ex:316
msgid "next year"
msgstr "next year"

#: lib/l10n/translator.ex:321
msgid "this month"
msgstr "this month"

#: lib/l10n/translator.ex:327
msgid "this week"
msgstr "this week"

#: lib/l10n/translator.ex:315
msgid "this year"
msgstr "this year"

#: lib/l10n/translator.ex:333
msgid "today"
msgstr "today"

#: lib/l10n/translator.ex:334
msgid "tomorrow"
msgstr "tomorrow"

#: lib/l10n/translator.ex:332
msgid "yesterday"
msgstr "yesterday"

#: lib/l10n/translator.ex:336
msgid "%{count} day ago"
msgid_plural "%{count} days ago"
msgstr[0] "%{count} day ago"
msgstr[1] "%{count} days ago"

#: lib/l10n/translator.ex:361
msgid "%{count} hour ago"
msgid_plural "%{count} hours ago"
msgstr[0] "%{count} hour ago"
msgstr[1] "%{count} hours ago"

#: lib/l10n/translator.ex:364
msgid "%{count} minute ago"
msgid_plural "%{count} minutes ago"
msgstr[0] "%{count} minute ago"
msgstr[1] "%{count} minutes ago"

#: lib/l10n/translator.ex:324
msgid "%{count} month ago"
msgid_plural "%{count} months ago"
msgstr[0] "%{count} month ago"
msgstr[1] "%{count} months ago"

#: lib/l10n/translator.ex:367
msgid "%{count} second ago"
msgid_plural "%{count} seconds ago"
msgstr[0] "%{count} second ago"
msgstr[1] "%{count} seconds ago"

#: lib/l10n/translator.ex:330
msgid "%{count} week ago"
msgid_plural "%{count} weeks ago"
msgstr[0] "%{count} week ago"
msgstr[1] "%{count} weeks ago"

#: lib/l10n/translator.ex:318
msgid "%{count} year ago"
msgid_plural "%{count} years ago"
msgstr[0] "%{count} year ago"
msgstr[1] "%{count} years ago"

#: lib/l10n/translator.ex:335
msgid "in %{count} day"
msgid_plural "in %{count} days"
msgstr[0] "in %{count} day"
msgstr[1] "in %{count} days"

#: lib/l10n/translator.ex:360
msgid "in %{count} hour"
msgid_plural "in %{count} hours"
msgstr[0] "in %{count} hour"
msgstr[1] "in %{count} hours"

#: lib/l10n/translator.ex:363
msgid "in %{count} minute"
msgid_plural "in %{count} minutes"
msgstr[0] "in %{count} minute"
msgstr[1] "in %{count} minutes"

#: lib/l10n/translator.ex:323
msgid "in %{count} month"
msgid_plural "in %{count} months"
msgstr[0] "in %{count} month"
msgstr[1] "in %{count} months"

#: lib/l10n/translator.ex:366
msgid "in %{count} second"
msgid_plural "in %{count} seconds"
msgstr[0] "in %{count} second"
msgstr[1] "in %{count} seconds"

#: lib/l10n/translator.ex:329
msgid "in %{count} week"
msgid_plural "in %{count} weeks"
msgstr[0] "in %{count} week"
msgstr[1] "in %{count} weeks"

#: lib/l10n/translator.ex:317
msgid "in %{count} year"
msgid_plural "in %{count} years"
msgstr[0] "in %{count} year"
msgstr[1] "in %{count} years"

#: lib/l10n/translator.ex:350
msgid "last friday"
msgstr "last friday"

#: lib/l10n/translator.ex:338
msgid "last monday"
msgstr "last monday"

#: lib/l10n/translator.ex:353
msgid "last saturday"
msgstr "last saturday"

#: lib/l10n/translator.ex:356
msgid "last sunday"
msgstr "last sunday"

#: lib/l10n/translator.ex:347
msgid "last thursday"
msgstr "last thursday"

#: lib/l10n/translator.ex:341
msgid "last tuesday"
msgstr "last tuesday"

#: lib/l10n/translator.ex:344
msgid "last wednesday"
msgstr "last wednesday"

#: lib/l10n/translator.ex:352
msgid "next friday"
msgstr "next friday"

#: lib/l10n/translator.ex:340
msgid "next monday"
msgstr "next monday"

#: lib/l10n/translator.ex:355
msgid "next saturday"
msgstr "next saturday"

#: lib/l10n/translator.ex:358
msgid "next sunday"
msgstr "next sunday"

#: lib/l10n/translator.ex:349
msgid "next thursday"
msgstr "next thursday"

#: lib/l10n/translator.ex:343
msgid "next tuesday"
msgstr "next tuesday"

#: lib/l10n/translator.ex:346
msgid "next wednesday"
msgstr "next wednesday"

#: lib/l10n/translator.ex:351
msgid "this friday"
msgstr "this friday"

#: lib/l10n/translator.ex:339
msgid "this monday"
msgstr "this monday"

#: lib/l10n/translator.ex:354
msgid "this saturday"
msgstr "this saturday"

#: lib/l10n/translator.ex:357
msgid "this sunday"
msgstr "this sunday"

#: lib/l10n/translator.ex:348
msgid "this thursday"
msgstr "this thursday"

#: lib/l10n/translator.ex:342
msgid "this tuesday"
msgstr "this tuesday"

#: lib/l10n/translator.ex:345
msgid "this wednesday"
msgstr "this wednesday"

#: lib/l10n/translator.ex:369
msgid "now"
msgstr "now"
