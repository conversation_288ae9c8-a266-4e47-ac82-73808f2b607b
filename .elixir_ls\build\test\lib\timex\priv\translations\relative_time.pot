## This file is a PO Template file.
##
## `msgid`s here are often extracted from source code.
## Add new translations manually only if they're dynamic
## translations that can't be statically extracted.
##
## Run `mix gettext.extract` to bring this file up to
## date. Leave `msgstr`s empty as changing them here as no
## effect: edit them in PO (`.po`) files instead.
msgid ""
msgstr ""
"Language: INSERT LANGUAGE HERE\n"

#: lib/l10n/translator.ex:320
msgid "last month"
msgstr ""

#: lib/l10n/translator.ex:326
msgid "last week"
msgstr ""

#: lib/l10n/translator.ex:314
msgid "last year"
msgstr ""

#: lib/l10n/translator.ex:322
msgid "next month"
msgstr ""

#: lib/l10n/translator.ex:328
msgid "next week"
msgstr ""

#: lib/l10n/translator.ex:316
msgid "next year"
msgstr ""

#: lib/l10n/translator.ex:321
msgid "this month"
msgstr ""

#: lib/l10n/translator.ex:327
msgid "this week"
msgstr ""

#: lib/l10n/translator.ex:315
msgid "this year"
msgstr ""

#: lib/l10n/translator.ex:333
msgid "today"
msgstr ""

#: lib/l10n/translator.ex:334
msgid "tomorrow"
msgstr ""

#: lib/l10n/translator.ex:332
msgid "yesterday"
msgstr ""

#: lib/l10n/translator.ex:336
msgid "%{count} day ago"
msgid_plural "%{count} days ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:361
msgid "%{count} hour ago"
msgid_plural "%{count} hours ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:364
msgid "%{count} minute ago"
msgid_plural "%{count} minutes ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:324
msgid "%{count} month ago"
msgid_plural "%{count} months ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:367
msgid "%{count} second ago"
msgid_plural "%{count} seconds ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:330
msgid "%{count} week ago"
msgid_plural "%{count} weeks ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:318
msgid "%{count} year ago"
msgid_plural "%{count} years ago"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:335
msgid "in %{count} day"
msgid_plural "in %{count} days"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:360
msgid "in %{count} hour"
msgid_plural "in %{count} hours"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:363
msgid "in %{count} minute"
msgid_plural "in %{count} minutes"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:323
msgid "in %{count} month"
msgid_plural "in %{count} months"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:366
msgid "in %{count} second"
msgid_plural "in %{count} seconds"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:329
msgid "in %{count} week"
msgid_plural "in %{count} weeks"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:317
msgid "in %{count} year"
msgid_plural "in %{count} years"
msgstr[0] ""
msgstr[1] ""

#: lib/l10n/translator.ex:350
msgid "last friday"
msgstr ""

#: lib/l10n/translator.ex:338
msgid "last monday"
msgstr ""

#: lib/l10n/translator.ex:353
msgid "last saturday"
msgstr ""

#: lib/l10n/translator.ex:356
msgid "last sunday"
msgstr ""

#: lib/l10n/translator.ex:347
msgid "last thursday"
msgstr ""

#: lib/l10n/translator.ex:341
msgid "last tuesday"
msgstr ""

#: lib/l10n/translator.ex:344
msgid "last wednesday"
msgstr ""

#: lib/l10n/translator.ex:352
msgid "next friday"
msgstr ""

#: lib/l10n/translator.ex:340
msgid "next monday"
msgstr ""

#: lib/l10n/translator.ex:355
msgid "next saturday"
msgstr ""

#: lib/l10n/translator.ex:358
msgid "next sunday"
msgstr ""

#: lib/l10n/translator.ex:349
msgid "next thursday"
msgstr ""

#: lib/l10n/translator.ex:343
msgid "next tuesday"
msgstr ""

#: lib/l10n/translator.ex:346
msgid "next wednesday"
msgstr ""

#: lib/l10n/translator.ex:351
msgid "this friday"
msgstr ""

#: lib/l10n/translator.ex:339
msgid "this monday"
msgstr ""

#: lib/l10n/translator.ex:354
msgid "this saturday"
msgstr ""

#: lib/l10n/translator.ex:357
msgid "this sunday"
msgstr ""

#: lib/l10n/translator.ex:348
msgid "this thursday"
msgstr ""

#: lib/l10n/translator.ex:342
msgid "this tuesday"
msgstr ""

#: lib/l10n/translator.ex:345
msgid "this wednesday"
msgstr ""

#: lib/l10n/translator.ex:369
msgid "now"
msgstr ""
