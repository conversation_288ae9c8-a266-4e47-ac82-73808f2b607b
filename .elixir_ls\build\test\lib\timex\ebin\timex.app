{application,timex,
             [{compile_env,[{timex,['Elixir.Timex.Gettext'],error}]},
              {applications,[kernel,stdlib,elixir,tzdata,combine,gettext]},
              {description,"Timex is a rich, comprehensive Date/Time library for Elixir projects, with full timezone support via the :tzdata package.\nIf you need to manipulate dates, times, datetimes, timestamps, etc., then Timex is for you!\n"},
              {modules,['Elixir.Enumerable.Timex.Interval',
                        'Elixir.Inspect.Timex.AmbiguousDateTime',
                        'Elixir.Inspect.Timex.AmbiguousTimezoneInfo',
                        'Elixir.Inspect.Timex.Duration',
                        'Elixir.Inspect.Timex.TimezoneInfo','Elixir.Timex',
                        'Elixir.Timex.AmbiguousDateTime',
                        'Elixir.Timex.AmbiguousTimezoneInfo',
                        'Elixir.Timex.Calendar.Julian',
                        'Elixir.Timex.Comparable',
                        'Elixir.Timex.Comparable.Date',
                        'Elixir.Timex.Comparable.DateTime',
                        'Elixir.Timex.Comparable.Diff',
                        'Elixir.Timex.Comparable.NaiveDateTime',
                        'Elixir.Timex.Comparable.Timex.AmbiguousDateTime',
                        'Elixir.Timex.Comparable.Tuple',
                        'Elixir.Timex.Comparable.Utils',
                        'Elixir.Timex.Constants','Elixir.Timex.Convert',
                        'Elixir.Timex.ConvertError','Elixir.Timex.Date',
                        'Elixir.Timex.DateTime',
                        'Elixir.Timex.DateTime.Helpers',
                        'Elixir.Timex.Duration',
                        'Elixir.Timex.Format.DateTime.Formatter',
                        'Elixir.Timex.Format.DateTime.Formatters.Default',
                        'Elixir.Timex.Format.DateTime.Formatters.Relative',
                        'Elixir.Timex.Format.DateTime.Formatters.Strftime',
                        'Elixir.Timex.Format.Duration.Formatter',
                        'Elixir.Timex.Format.Duration.Formatters.Default',
                        'Elixir.Timex.Format.Duration.Formatters.Humanized',
                        'Elixir.Timex.Format.FormatError',
                        'Elixir.Timex.Gettext','Elixir.Timex.Helpers',
                        'Elixir.Timex.Interval',
                        'Elixir.Timex.Interval.FormatError',
                        'Elixir.Timex.Macros','Elixir.Timex.NaiveDateTime',
                        'Elixir.Timex.Parse.DateTime.Helpers',
                        'Elixir.Timex.Parse.DateTime.Parser',
                        'Elixir.Timex.Parse.DateTime.Parsers',
                        'Elixir.Timex.Parse.DateTime.Parsers.ISO8601Extended',
                        'Elixir.Timex.Parse.DateTime.Tokenizer',
                        'Elixir.Timex.Parse.DateTime.Tokenizers.Default',
                        'Elixir.Timex.Parse.DateTime.Tokenizers.Directive',
                        'Elixir.Timex.Parse.DateTime.Tokenizers.Strftime',
                        'Elixir.Timex.Parse.Duration.Parser',
                        'Elixir.Timex.Parse.Duration.Parsers.ISO8601Parser',
                        'Elixir.Timex.Parse.ParseError',
                        'Elixir.Timex.Parse.Timezones.Posix',
                        'Elixir.Timex.Parse.ZoneInfo.Parser',
                        'Elixir.Timex.Parse.ZoneInfo.Parser.Header',
                        'Elixir.Timex.Parse.ZoneInfo.Parser.LeapSecond',
                        'Elixir.Timex.Parse.ZoneInfo.Parser.Rule',
                        'Elixir.Timex.Parse.ZoneInfo.Parser.TransitionInfo',
                        'Elixir.Timex.Parse.ZoneInfo.Parser.Zone',
                        'Elixir.Timex.PosixTimezone','Elixir.Timex.Protocol',
                        'Elixir.Timex.Protocol.Any',
                        'Elixir.Timex.Protocol.Date',
                        'Elixir.Timex.Protocol.DateTime',
                        'Elixir.Timex.Protocol.Map',
                        'Elixir.Timex.Protocol.NaiveDateTime',
                        'Elixir.Timex.Protocol.Tuple','Elixir.Timex.Time',
                        'Elixir.Timex.Timezone',
                        'Elixir.Timex.Timezone.Database',
                        'Elixir.Timex.Timezone.Local',
                        'Elixir.Timex.Timezone.Utils',
                        'Elixir.Timex.TimezoneInfo','Elixir.Timex.Translator',
                        'Elixir.Timex.Types']},
              {registered,[]},
              {vsn,"3.7.13"},
              {env,[{local_timezone,nil}]},
              {mod,{'Elixir.Timex',[]}}]}.
