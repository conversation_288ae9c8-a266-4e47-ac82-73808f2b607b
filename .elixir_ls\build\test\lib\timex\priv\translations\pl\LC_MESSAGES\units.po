# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: pl\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7.1\n"

#: lib/l10n/translator.ex:261
msgid "%{count} day"
msgid_plural "%{count} days"
msgstr[0] "%{count} dzień"
msgstr[1] "%{count} dni"
msgstr[2] "%{count} dni"

#: lib/l10n/translator.ex:260
msgid "%{count} hour"
msgid_plural "%{count} hours"
msgstr[0] "%{count} godzina"
msgstr[1] "%{count} godziny"
msgstr[2] "%{count} godzin"

#: lib/l10n/translator.ex:256
msgid "%{count} microsecond"
msgid_plural "%{count} microseconds"
msgstr[0] "%{count} mikrosekunda"
msgstr[1] "%{count} mikrosekundy"
msgstr[2] "%{count} mikrosekund"

#: lib/l10n/translator.ex:257
msgid "%{count} millisecond"
msgid_plural "%{count} milliseconds"
msgstr[0] "%{count} milisekunda"
msgstr[1] "%{count} milisekundy"
msgstr[2] "%{count} milisekund"

#: lib/l10n/translator.ex:259
msgid "%{count} minute"
msgid_plural "%{count} minutes"
msgstr[0] "%{count} minuta"
msgstr[1] "%{count} minuty"
msgstr[2] "%{count} minut"

#: lib/l10n/translator.ex:263
msgid "%{count} month"
msgid_plural "%{count} months"
msgstr[0] "%{count} miesiąc"
msgstr[1] "%{count} miesiące"
msgstr[2] "%{count} miesięcy"

#: lib/l10n/translator.ex:255
msgid "%{count} nanosecond"
msgid_plural "%{count} nanoseconds"
msgstr[0] "%{count} nanosekunda"
msgstr[1] "%{count} nanosekundy"
msgstr[2] "%{count} nanosekund"

#: lib/l10n/translator.ex:258
msgid "%{count} second"
msgid_plural "%{count} seconds"
msgstr[0] "%{count} sekunda"
msgstr[1] "%{count} sekundy"
msgstr[2] "%{count} sekund"

#: lib/l10n/translator.ex:262
msgid "%{count} week"
msgid_plural "%{count} weeks"
msgstr[0] "%{count} tydzień"
msgstr[1] "%{count} tygodnie"
msgstr[2] "%{count} tygodni"

#: lib/l10n/translator.ex:264
msgid "%{count} year"
msgid_plural "%{count} years"
msgstr[0] "%{count} rok"
msgstr[1] "%{count} lata"
msgstr[2] "%{count} lat"
