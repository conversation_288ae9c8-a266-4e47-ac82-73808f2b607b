defmodule Reconciliation.Transaction do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.{ReconciliationRun, UploadedFile, TransactionMatch}

  schema "transactions" do
    field :row_number, :integer
    field :transaction_date, :date
    field :transaction_id, :string
    field :reference, :string
    field :description, :string
    field :amount, :decimal
    field :transaction_type, :string
    field :account, :string
    field :category, :string
    field :currency, :string, default: "USD"
    field :raw_data, :map
    field :is_matched, :boolean, default: false
    field :match_confidence, :decimal
    field :validation_errors, {:array, :string}

    belongs_to :uploaded_file, UploadedFile
    belongs_to :reconciliation_run, ReconciliationRun
    has_many :matches_as_a, TransactionMatch, foreign_key: :transaction_a_id
    has_many :matches_as_b, TransactionMatch, foreign_key: :transaction_b_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(transaction, attrs) do
    transaction
    |> cast(attrs, [
      :row_number, :transaction_date, :transaction_id, :reference, :description,
      :amount, :transaction_type, :account, :category, :currency, :raw_data,
      :is_matched, :match_confidence, :validation_errors, :uploaded_file_id,
      :reconciliation_run_id
    ])
    |> validate_required([:row_number, :amount, :uploaded_file_id, :reconciliation_run_id])
    |> validate_number(:amount, not_equal_to: 0)
    |> validate_number(:row_number, greater_than: 0)
    |> validate_number(:match_confidence, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    |> validate_length(:currency, is: 3)
    |> validate_inclusion(:transaction_type, ["debit", "credit", "transfer", "fee", "interest", "other"])
    |> foreign_key_constraint(:uploaded_file_id)
    |> foreign_key_constraint(:reconciliation_run_id)
  end

  @doc """
  Creates a changeset for updating match status
  """
  def match_changeset(transaction, attrs) do
    transaction
    |> cast(attrs, [:is_matched, :match_confidence])
    |> validate_number(:match_confidence, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
  end

  @doc """
  Creates a changeset for validation errors
  """
  def validation_changeset(transaction, errors) do
    transaction
    |> cast(%{validation_errors: errors}, [:validation_errors])
  end

  @doc """
  Checks if the transaction is matched
  """
  def matched?(%__MODULE__{is_matched: true}), do: true
  def matched?(_), do: false

  @doc """
  Checks if the transaction is unmatched
  """
  def unmatched?(%__MODULE__{is_matched: false}), do: true
  def unmatched?(_), do: false

  @doc """
  Returns the absolute amount (always positive)
  """
  def abs_amount(%__MODULE__{amount: amount}) do
    Decimal.abs(amount)
  end

  @doc """
  Checks if the transaction is a debit (negative amount or debit type)
  """
  def debit?(%__MODULE__{amount: amount, transaction_type: type}) do
    Decimal.negative?(amount) or type == "debit"
  end

  @doc """
  Checks if the transaction is a credit (positive amount or credit type)
  """
  def credit?(%__MODULE__{amount: amount, transaction_type: type}) do
    Decimal.positive?(amount) or type == "credit"
  end

  @doc """
  Formats the amount with currency symbol
  """
  def formatted_amount(%__MODULE__{amount: amount, currency: currency}) do
    symbol = currency_symbol(currency)
    "#{symbol}#{Decimal.to_string(amount, :normal)}"
  end

  @doc """
  Returns currency symbol for common currencies
  """
  def currency_symbol("USD"), do: "$"
  def currency_symbol("EUR"), do: "€"
  def currency_symbol("GBP"), do: "£"
  def currency_symbol("JPY"), do: "¥"
  def currency_symbol("CAD"), do: "C$"
  def currency_symbol("AUD"), do: "A$"
  def currency_symbol(_), do: ""

  @doc """
  Checks if two transactions could potentially match based on amount
  """
  def amount_match?(%__MODULE__{amount: amount1}, %__MODULE__{amount: amount2}, tolerance \\ Decimal.new("0.01")) do
    diff = Decimal.sub(amount1, amount2) |> Decimal.abs()
    Decimal.compare(diff, tolerance) != :gt
  end

  @doc """
  Checks if two transactions could potentially match based on date
  """
  def date_match?(transaction1, transaction2, tolerance_days \\ 3)
  def date_match?(%__MODULE__{transaction_date: nil}, %__MODULE__{}, _), do: false
  def date_match?(%__MODULE__{}, %__MODULE__{transaction_date: nil}, _), do: false
  def date_match?(%__MODULE__{transaction_date: date1}, %__MODULE__{transaction_date: date2}, tolerance_days) do
    diff = Date.diff(date1, date2) |> abs()
    diff <= tolerance_days
  end

  @doc """
  Checks if two transactions could potentially match based on reference
  """
  def reference_match?(%__MODULE__{reference: nil}, %__MODULE__{}, _), do: false
  def reference_match?(%__MODULE__{}, %__MODULE__{reference: nil}, _), do: false
  def reference_match?(%__MODULE__{reference: ref1}, %__MODULE__{reference: ref2}, _) when ref1 == ref2, do: true
  def reference_match?(_, _, _), do: false

  @doc """
  Calculates a similarity score between two transactions
  """
  def similarity_score(%__MODULE__{} = t1, %__MODULE__{} = t2, opts \\ []) do
    amount_tolerance = Keyword.get(opts, :amount_tolerance, Decimal.new("0.01"))
    date_tolerance = Keyword.get(opts, :date_tolerance, 3)

    score = 0

    # Amount match (40% weight)
    score = if amount_match?(t1, t2, amount_tolerance), do: score + 40, else: score

    # Date match (30% weight)
    score = if date_match?(t1, t2, date_tolerance), do: score + 30, else: score

    # Reference match (20% weight)
    score = if reference_match?(t1, t2, nil), do: score + 20, else: score

    # Description similarity (10% weight)
    score = if description_similarity(t1.description, t2.description) > 0.7, do: score + 10, else: score

    score
  end

  # Private function to calculate description similarity
  defp description_similarity(nil, _), do: 0.0
  defp description_similarity(_, nil), do: 0.0
  defp description_similarity(desc1, desc2) when desc1 == desc2, do: 1.0
  defp description_similarity(desc1, desc2) do
    # Simple word-based similarity
    words1 = String.split(String.downcase(desc1))
    words2 = String.split(String.downcase(desc2))

    common_words = MapSet.intersection(MapSet.new(words1), MapSet.new(words2))
    total_words = MapSet.union(MapSet.new(words1), MapSet.new(words2))

    if MapSet.size(total_words) > 0 do
      MapSet.size(common_words) / MapSet.size(total_words)
    else
      0.0
    end
  end
end
