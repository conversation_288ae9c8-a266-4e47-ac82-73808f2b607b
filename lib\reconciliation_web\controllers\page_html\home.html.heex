<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReconcilePro - AI-Powered Financial Reconciliation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-bg: #f0f0f0;
            --darker-bg: #060812;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: #a0a9c0;
            --neon-blue: #00d4ff;
            --neon-purple: #8b5cf6;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                       radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }

        /* Glassmorphism utilities */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 20px 0;
            transition: all 0.3s ease;
        }

        .nav.scrolled {
            background: rgba(10, 14, 26, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--neon-blue);
            transform: translateY(-2px);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: var(--accent-gradient);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .nav-cta {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--accent-gradient);
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(79, 172, 254, 0.4);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid var(--glass-border);
        }

        .btn-ghost:hover {
            background: var(--glass-bg);
            transform: translateY(-3px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 0 2rem;
            overflow: hidden;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #a0a9c0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content .highlight {
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .hero-content p {
            font-size: 1.3rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Hero Visual */
        .hero-visual {
            position: relative;
            height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-card {
            position: absolute;
            padding: 1.5rem;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: float-card 6s ease-in-out infinite;
        }

        .floating-card:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-card:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-card:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float-card {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }

        .dashboard-preview {
            width: 100%;
            max-width: 500px;
            height: 350px;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        /* Sections */
        .section {
            padding: 6rem 2rem;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-subtitle {
            font-size: 1rem;
            color: var(--neon-blue);
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #a0a9c0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-description {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            padding: 2.5rem;
            border-radius: 24px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(79, 172, 254, 0.3);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* How it works */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .step {
            text-align: center;
            position: relative;
        }

        .step-number {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 2rem;
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .step-description {
            color: var(--text-secondary);
        }

        /* Testimonials */
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .testimonial {
            padding: 2.5rem;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border-radius: 24px;
            border: 1px solid var(--glass-border);
            position: relative;
        }

        .testimonial-quote {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-gradient);
        }

        .author-info h4 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .author-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
            border-radius: 40px;
            padding: 4rem;
            text-align: center;
            margin: 4rem 0;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .cta-description {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            padding: 3rem 2rem 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: var(--darker-bg);
        }

        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .footer-section a {
            color: var(--text-secondary);
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: var(--neon-blue);
        }

        .footer-bottom {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease forwards;
        }

        /* Particle system */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--neon-blue);
            border-radius: 50%;
            opacity: 0.6;
            animation: particle-float 15s linear infinite;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="bg-blue-900">
    <!-- Navigation -->
    <nav class="nav" id="navbar">
        <div class="nav-container">
            <div class="logo">ReconcilePro</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#how-it-works">How it Works</a></li>
                <li><a href="#testimonials">Testimonials</a></li>
                <li><a href="#pricing">Pricing</a></li>
            </ul>
            <div class="nav-cta">
                <a href="/users/log_in" class="btn btn-ghost">Sign In</a>
                <a href="#" class="btn btn-primary">Start Free Trial</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="particles"></div>
        <div class="hero-container">
            <div class="hero-content fade-in">
                <h1>
                    AI-Powered <br>
                    <span class="highlight">Financial Reconciliation</span><br>
                    Made Simple
                </h1>
                <p>
                    Transform your financial operations with intelligent automation. 
                    Reconcile millions of transactions in seconds, detect anomalies instantly, 
                    and maintain perfect accuracy across all your financial data.
                </p>
                <div class="hero-cta">
                    <a href="#" class="btn btn-primary">Start Free Trial</a>
                    <a href="#" class="btn btn-ghost">Watch Demo</a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">Accuracy Rate</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">10M+</div>
                        <div class="stat-label">Transactions/Day</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">Time Savings</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="dashboard-preview glass-card">
                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, rgba(79, 172, 254, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; color: var(--text-primary);">
                        Interactive Dashboard Preview
                    </div>
                </div>
                <div class="floating-card">
                    <div style="color: var(--neon-blue); font-weight: 600;">✓ Transaction Matched</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">$2,847.92 • Bank Transfer</div>
                </div>
                <div class="floating-card">
                    <div style="color: var(--neon-purple); font-weight: 600;">⚠ Discrepancy Found</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">$145.00 • Investigate</div>
                </div>
                <div class="floating-card">
                    <div style="color: #10b981; font-weight: 600;">📊 Report Generated</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Monthly Reconciliation</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section" id="features">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Powerful Features</div>
                <h2 class="section-title">Everything You Need for Perfect Reconciliation</h2>
                <p class="section-description">
                    Our AI-powered platform combines cutting-edge technology with intuitive design 
                    to deliver unmatched accuracy and efficiency in financial reconciliation.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Lightning-Fast AI Matching</h3>
                    <p class="feature-description">
                        Our advanced AI engine processes millions of transactions in seconds, 
                        automatically matching entries with 99.9% accuracy using machine learning algorithms.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Intelligent Anomaly Detection</h3>
                    <p class="feature-description">
                        Proactively identify discrepancies, unusual patterns, and potential fraud 
                        with our smart detection system that learns from your data.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Real-Time Dashboards</h3>
                    <p class="feature-description">
                        Get instant visibility into your reconciliation status with beautiful, 
                        interactive dashboards and real-time alerts.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">95% Time Reduction</h3>
                    <p class="feature-description">
                        Eliminate manual processes and reduce reconciliation time from days to minutes 
                        with our automated workflow engine.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M12 15v5l3-3m-3 3l-3-3m3 3V9a3 3 0 013-3h.01M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h5.5"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Enterprise Integration</h3>
                    <p class="feature-description">
                        Connect seamlessly with your existing ERP, banking, and accounting systems 
                        through our robust API and pre-built connectors.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Bank-Grade Security</h3>
                    <p class="feature-description">
                        Your data is protected with enterprise-grade encryption, SOC 2 compliance, 
                        and multi-factor authentication.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="section" id="how-it-works">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">How It Works</div>
                <h2 class="section-title">Three Simple Steps to Financial Clarity</h2>
                <p class="section-description">
                    Get started in minutes with our intuitive platform designed for finance professionals.
                </p>
            </div>
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Connect Your Data Sources</h3>
                    <p class="step-description">
                        Securely connect your banks, ERPs, and financial systems. 
                        Our platform supports 500+ integrations with automatic data sync.
                    </p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3 class="step-title">AI-Powered Processing</h3>
                    <p class="step-description">
                        Our advanced AI analyzes your data, identifies patterns, and automatically 
                        matches transactions with industry-leading accuracy.
                    </p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Review & Export</h3>
                    <p class="step-description">
                        Review matches, investigate exceptions, and generate comprehensive reports. 
                        Export results to your preferred format with full audit trails.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="section" id="testimonials">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Customer Success</div>
                <h2 class="section-title">Trusted by Finance Leaders Worldwide</h2>
                <p class="section-description">
                    See how companies are transforming their financial operations with ReconcilePro.
                </p>
            </div>
            <div class="testimonials-grid">
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "ReconcilePro reduced our month-end reconciliation from 5 days to 2 hours. 
                        The AI accuracy is incredible - we've eliminated manual errors completely."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Sarah Chen</h4>
                            <p>CFO, TechFlow Solutions</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "The real-time anomaly detection has saved us millions in fraud prevention. 
                        Best investment we've made in our finance tech stack."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Marcus Rodriguez</h4>
                            <p>Finance Director, Global Bank Corp</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "Implementation was seamless. Within weeks, we were processing 10x more 
                        transactions with half the team. Game-changing technology."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Lisa Thompson</h4>
                            <p>Head of Accounting, MegaCorp Inc</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "The reporting capabilities are phenomenal. Board presentations have 
                        never been easier with their automated compliance reports."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>David Park</h4>
                            <p>Controller, Innovation Labs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="section" id="pricing">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Pricing</div>
                <h2 class="section-title">Choose Your Perfect Plan</h2>
                <p class="section-description">
                    Transparent pricing that scales with your business. No hidden fees, no surprises.
                </p>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 2rem; margin-top: 4rem;">
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Starter</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">MK299</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">per month</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Up to 100K transactions/month
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Basic AI matching
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Standard reporting
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Email support
                        </li>
                    </ul>
                    <a href="#" class="btn btn-ghost" style="width: 100%;">Get Started</a>
                </div>
                
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative; border: 2px solid var(--neon-blue); transform: scale(1.05);">
                    <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: var(--accent-gradient); padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">MOST POPULAR</div>
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Professional</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">MK799</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">per month</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Up to 1M transactions/month
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Advanced AI + anomaly detection
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Real-time dashboards
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> API integrations
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Priority support
                        </li>
                    </ul>
                    <a href="#" class="btn btn-primary" style="width: 100%;">Start Free Trial</a>
                </div>
                
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Enterprise</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">Custom</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">pricing</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Unlimited transactions
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Custom AI models
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> White-label solution
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Dedicated account manager
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> 24/7 support
                        </li>
                    </ul>
                    <a href="#" class="btn btn-ghost" style="width: 100%;">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section">
        <div class="container">
            <div class="cta-section">
                <h2 class="cta-title">Ready to Transform Your Financial Operations?</h2>
                <p class="cta-description">
                    Join thousands of finance professionals who trust ReconcilePro for accurate, 
                    fast, and reliable financial reconciliation.
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="#" class="btn btn-primary" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        Start Your Free Trial
                    </a>
                    <a href="#" class="btn btn-ghost" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        Schedule Demo
                    </a>
                </div>
                <p style="margin-top: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                    ✓ No credit card required • ✓ 14-day free trial • ✓ Setup in 5 minutes
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>ReconcilePro</h3>
                <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                    The future of financial reconciliation, powered by AI.
                </p>
                <div style="display: flex; gap: 1rem;">
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">📧</a>
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">💬</a>
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">📱</a>
                </div>
            </div>
            <div class="footer-section">
                <h3>Product</h3>
                <a href="#">Features</a>
                <a href="#">Pricing</a>
                <a href="#">Integrations</a>
                <a href="#">API Documentation</a>
                <a href="#">Security</a>
            </div>
            <div class="footer-section">
                <h3>Company</h3>
                <a href="#">About Us</a>
                <a href="#">Careers</a>
                <a href="#">Press</a>
                <a href="#">Partners</a>
                <a href="#">Contact</a>
            </div>
            <div class="footer-section">
                <h3>Resources</h3>
                <a href="#">Blog</a>
                <a href="#">Help Center</a>
                <a href="#">Webinars</a>
                <a href="#">Case Studies</a>
                <a href="#">Status Page</a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 ReconcilePro. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
    </footer>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Particle system
        function createParticles() {
            const particlesContainer = document.querySelector('.particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            
            // Observe all sections for animation
            document.querySelectorAll('.section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'all 0.8s ease';
                observer.observe(section);
            });

            // Add hover effects to cards
            document.querySelectorAll('.feature-card, .testimonial').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Dynamic text animation for hero
        const heroTitle = document.querySelector('.hero-content h1');
        if (heroTitle) {
            const text = heroTitle.innerHTML;
            heroTitle.innerHTML = '';
            let i = 0;
            
            function typeWriter() {
                if (i < text.length) {
                    heroTitle.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            }
            
            setTimeout(typeWriter, 1000);
        }
    </script>
</body>
</html>