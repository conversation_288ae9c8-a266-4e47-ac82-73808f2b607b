<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <xs:element name="persons" type="PersonsType"/>
    <xs:complexType name="PersonsType">
        <xs:sequence>
            <xs:element name="person" minOccurs="1" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:all>
                        <xs:element name="familyName" type="xs:string" minOccurs="0" maxOccurs="1"/>
                        <xs:element name="firstName" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    </xs:all>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
