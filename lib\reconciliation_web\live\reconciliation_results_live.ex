defmodule ReconciliationWeb.ReconciliationResultsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{ReconciliationRun, Transaction, TransactionMatch}

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    user = socket.assigns.current_user
    
    case Reconciliation.get_user_reconciliation_run(id, user.id) do
      nil ->
        {:ok, 
         socket
         |> put_flash(:error, "Reconciliation run not found")
         |> push_navigate(to: ~p"/reconciliation")
        }
      
      run ->
        # Subscribe to updates if processing
        if run.status in ["pending", "processing"] do
          Phoenix.PubSub.subscribe(Reconciliation.PubSub, "reconciliation:#{run.id}")
        end
        
        {:ok,
         socket
         |> assign(:page_title, "Reconciliation Results - #{run.name}")
         |> assign(:reconciliation_run, run)
         |> assign(:stats, Reconciliation.get_reconciliation_stats(run.id))
         |> assign(:transactions, Reconciliation.get_transactions(run.id))
         |> assign(:matches, Reconciliation.get_transaction_matches(run.id))
         |> assign(:selected_tab, "overview")
        }
    end
  end

  @impl true
  def handle_event("change_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :selected_tab, tab)}
  end

  @impl true
  def handle_event("verify_match", %{"match_id" => match_id}, socket) do
    match = Enum.find(socket.assigns.matches, &(&1.id == String.to_integer(match_id)))
    
    case Reconciliation.verify_transaction_match(match, true) do
      {:ok, _} ->
        updated_matches = Enum.map(socket.assigns.matches, fn m ->
          if m.id == match.id, do: %{m | verified_by_user: true}, else: m
        end)
        
        {:noreply, 
         socket
         |> assign(:matches, updated_matches)
         |> put_flash(:info, "Match verified successfully")
        }
      
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to verify match")}
    end
  end

  @impl true
  def handle_info({:reconciliation_updated, run}, socket) do
    {:noreply,
     socket
     |> assign(:reconciliation_run, run)
     |> assign(:stats, Reconciliation.get_reconciliation_stats(run.id))
    }
  end

  # Helper functions for the template
  def format_currency(amount) when is_nil(amount), do: "$0.00"
  def format_currency(amount) do
    "$#{Decimal.to_string(amount, :normal)}"
  end

  def format_percentage(percentage) when is_nil(percentage), do: "0%"
  def format_percentage(percentage) do
    "#{Decimal.to_string(percentage, :normal)}%"
  end

  def status_color("completed"), do: "green"
  def status_color("processing"), do: "yellow"
  def status_color("failed"), do: "red"
  def status_color(_), do: "gray"

  def status_icon("completed"), do: "hero-check-circle"
  def status_icon("processing"), do: "hero-clock"
  def status_icon("failed"), do: "hero-x-circle"
  def status_icon(_), do: "hero-question-mark-circle"

  def confidence_color(confidence) when confidence >= 90, do: "green"
  def confidence_color(confidence) when confidence >= 70, do: "yellow"
  def confidence_color(_), do: "red"

  def file_transactions(transactions, file_type) do
    Enum.filter(transactions, fn t -> t.uploaded_file.file_type == file_type end)
  end

  def matched_transactions(transactions) do
    Enum.filter(transactions, & &1.is_matched)
  end

  def unmatched_transactions(transactions) do
    Enum.filter(transactions, &(not &1.is_matched))
  end

  def group_transactions_by_match_status(transactions, matches) do
    matched_pairs = Enum.map(matches, fn match ->
      %{
        match: match,
        transaction_a: match.transaction_a,
        transaction_b: match.transaction_b
      }
    end)
    
    matched_transaction_ids = 
      matches
      |> Enum.flat_map(fn match -> [match.transaction_a_id, match.transaction_b_id] end)
      |> MapSet.new()
    
    unmatched = Enum.reject(transactions, fn t -> 
      MapSet.member?(matched_transaction_ids, t.id) 
    end)
    
    unmatched_a = Enum.filter(unmatched, fn t -> 
      t.uploaded_file.file_type == "file_a" 
    end)
    
    unmatched_b = Enum.filter(unmatched, fn t -> 
      t.uploaded_file.file_type == "file_b" 
    end)
    
    %{
      matched_pairs: matched_pairs,
      unmatched_a: unmatched_a,
      unmatched_b: unmatched_b
    }
  end
end
