{application,erlsom,
             [{description,"XML parser. Supports SAX style parsing as well as XML Schema based data mapping: create records from XML (and vice versa)"},
              {vsn,"1.5.2"},
              {modules,[erlsom,erlsom_add,erlsom_compile,erlsom_example_value,
                        erlsom_lib,erlsom_parse,erlsom_parseXsd,erlsom_pass2,
                        erlsom_sax,erlsom_sax_latin1,erlsom_sax_latin9,
                        erlsom_sax_lib,erlsom_sax_list,erlsom_sax_utf16be,
                        erlsom_sax_utf16le,erlsom_sax_utf8,erlsom_simple_form,
                        erlsom_type2xsd,erlsom_ucs,erlsom_write,
                        erlsom_writeHrl,ucs]},
              {maintainers,["Willem <PERSON>"]},
              {licenses,["GNU Lesser GPL, Version 3"]},
              {links,[{"Github","https://github.com/willemdj/erlsom"}]},
              {registered,[]},
              {env,[]},
              {applications,[kernel,stdlib,inets]}]}.
