# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: Mak <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"

#: lib/l10n/translator.ex:290
msgid "Apr"
msgstr "tra"

#: lib/l10n/translator.ex:294
msgid "Aug"
msgstr "kol"

#: lib/l10n/translator.ex:298
msgid "Dec"
msgstr "pro"

#: lib/l10n/translator.ex:288
msgid "Feb"
msgstr "velj"

#: lib/l10n/translator.ex:287
msgid "Jan"
msgstr "sij"

#: lib/l10n/translator.ex:293
msgid "Jul"
msgstr "srp"

#: lib/l10n/translator.ex:292
msgid "Jun"
msgstr "lip"

#: lib/l10n/translator.ex:289
msgid "Mar"
msgstr "ožu"

#: lib/l10n/translator.ex:291
msgid "May"
msgstr "svi"

#: lib/l10n/translator.ex:297
msgid "Nov"
msgstr "stu"

#: lib/l10n/translator.ex:296
msgid "Oct"
msgstr "lis"

#: lib/l10n/translator.ex:295
msgid "Sep"
msgstr "ruj"
