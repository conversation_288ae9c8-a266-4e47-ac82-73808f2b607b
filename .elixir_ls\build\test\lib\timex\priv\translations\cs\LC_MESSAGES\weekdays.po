# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: cs\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.1\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "Pá"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "Pátek"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "Pon"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "Pondělí"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "Sob"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "Sobota"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "Ned"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "Neděle"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "Čtv"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "Čtvrtek"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "Út"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "Úterý"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "Stř"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "Středa"
