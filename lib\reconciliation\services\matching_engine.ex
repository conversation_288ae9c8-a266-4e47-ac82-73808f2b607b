defmodule Reconciliation.Services.MatchingEngine do
  @moduledoc """
  Service for matching transactions between two files in a reconciliation run.
  """

  alias Reconciliation.{
    Repo,
    Transaction,
    TransactionMatch,
    ReconciliationRun,
    ReconciliationSettings
  }

  require Logger

  @doc """
  Performs transaction matching for a reconciliation run.
  """
  def match_transactions(%ReconciliationRun{} = run) do
    Logger.info("Starting transaction matching for run #{run.id}")

    # Get user settings
    {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)

    # Get transactions from both files
    transactions = Reconciliation.get_transactions(run.id)

    file_a_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_b"
    end)

    Logger.info("Found #{length(file_a_transactions)} transactions in File A, #{length(file_b_transactions)} in File B")

    # Perform matching
    matches = find_matches(file_a_transactions, file_b_transactions, settings)

    # Create match records and update transaction statuses
    create_matches_and_update_transactions(matches, run.id)

    # Update reconciliation run statistics
    Reconciliation.calculate_reconciliation_stats(run.id)

    Logger.info("Completed transaction matching for run #{run.id}")
    {:ok, matches}
  end

  # Find matches between transactions from file A and file B
  defp find_matches(file_a_transactions, file_b_transactions, settings) do
    # First pass: Find exact matches
    {exact_matches, remaining_a, remaining_b} =
      find_exact_matches(file_a_transactions, file_b_transactions, settings)

    matched_b_ids = exact_matches
    |> Enum.map(& &1.transaction_b.id)
    |> MapSet.new()

    # Second pass: Find fuzzy matches if enabled
    fuzzy_matches = if settings.auto_match_fuzzy do
      find_fuzzy_matches(remaining_a, remaining_b, matched_b_ids, settings)
    else
      []
    end

    exact_matches ++ fuzzy_matches
  end

  # Find exact matches based on amount, date, and reference
  defp find_exact_matches(file_a_transactions, file_b_transactions, settings) do
    matched_a_ids = MapSet.new()
    matched_b_ids = MapSet.new()

    matches = for a_txn <- file_a_transactions,
                  b_txn <- file_b_transactions,
                  not MapSet.member?(matched_a_ids, a_txn.id),
                  not MapSet.member?(matched_b_ids, b_txn.id),
                  is_exact_match?(a_txn, b_txn, settings) do
      %{
        transaction_a: a_txn,
        transaction_b: b_txn,
        match_type: "exact",
        confidence_score: Decimal.new("100"),
        matching_criteria: ["amount", "date", "reference"]
      }
    end

    # Remove duplicates and ensure one-to-one matching
    unique_matches = ensure_one_to_one_matching(matches)

    matched_a_ids = unique_matches |> Enum.map(& &1.transaction_a.id) |> MapSet.new()
    matched_b_ids = unique_matches |> Enum.map(& &1.transaction_b.id) |> MapSet.new()

    remaining_a = Enum.reject(file_a_transactions, &MapSet.member?(matched_a_ids, &1.id))
    remaining_b = Enum.reject(file_b_transactions, &MapSet.member?(matched_b_ids, &1.id))

    {unique_matches, remaining_a, remaining_b}
  end

  # Find fuzzy matches based on similarity scores
  defp find_fuzzy_matches(file_a_transactions, file_b_transactions, already_matched_b_ids, settings) do
    threshold = settings.fuzzy_threshold_float()

    matches = for a_txn <- file_a_transactions,
                  b_txn <- file_b_transactions,
                  not MapSet.member?(already_matched_b_ids, b_txn.id) do

      similarity = Transaction.similarity_score(a_txn, b_txn, [
        amount_tolerance: settings.amount_tolerance,
        date_tolerance: settings.date_tolerance_days
      ])

      if similarity >= threshold * 100 do
        criteria = build_matching_criteria(a_txn, b_txn, settings)

        %{
          transaction_a: a_txn,
          transaction_b: b_txn,
          match_type: "fuzzy",
          confidence_score: Decimal.from_float(similarity),
          matching_criteria: criteria
        }
      else
        nil
      end
    end
    |> Enum.reject(&is_nil/1)
    |> Enum.sort_by(& Decimal.to_float(&1.confidence_score), :desc)

    # Ensure one-to-one matching for fuzzy matches too
    ensure_one_to_one_matching(matches)
  end

  # Check if two transactions are an exact match
  defp is_exact_match?(a_txn, b_txn, settings) do
    Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance) and
    Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days) and
    (is_nil(a_txn.reference) or is_nil(b_txn.reference) or
     Transaction.reference_match?(a_txn, b_txn, nil))
  end

  # Build list of matching criteria for a pair of transactions
  defp build_matching_criteria(a_txn, b_txn, settings) do
    criteria = []

    criteria = if Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance) do
      ["amount" | criteria]
    else
      criteria
    end

    criteria = if Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days) do
      ["date" | criteria]
    else
      criteria
    end

    criteria = if Transaction.reference_match?(a_txn, b_txn, nil) do
      ["reference" | criteria]
    else
      criteria
    end

    # Check description similarity
    criteria = if description_similar?(a_txn.description, b_txn.description) do
      ["description" | criteria]
    else
      criteria
    end

    Enum.reverse(criteria)
  end

  # Check if descriptions are similar
  defp description_similar?(nil, _), do: false
  defp description_similar?(_, nil), do: false
  defp description_similar?(desc1, desc2) when desc1 == desc2, do: true
  defp description_similar?(desc1, desc2) do
    # Simple word-based similarity check
    words1 = String.split(String.downcase(desc1))
    words2 = String.split(String.downcase(desc2))

    common_words = MapSet.intersection(MapSet.new(words1), MapSet.new(words2))
    total_words = MapSet.union(MapSet.new(words1), MapSet.new(words2))

    if MapSet.size(total_words) > 0 do
      similarity = MapSet.size(common_words) / MapSet.size(total_words)
      similarity > 0.6  # 60% similarity threshold
    else
      false
    end
  end

  # Ensure one-to-one matching (no transaction appears in multiple matches)
  defp ensure_one_to_one_matching(matches) do
    # Sort by confidence score (highest first)
    sorted_matches = Enum.sort_by(matches, & Decimal.to_float(&1.confidence_score), :desc)

    {final_matches, _, _} = Enum.reduce(sorted_matches, {[], MapSet.new(), MapSet.new()},
      fn match, {acc_matches, used_a_ids, used_b_ids} ->
        a_id = match.transaction_a.id
        b_id = match.transaction_b.id

        if MapSet.member?(used_a_ids, a_id) or MapSet.member?(used_b_ids, b_id) do
          # Skip this match as one of the transactions is already matched
          {acc_matches, used_a_ids, used_b_ids}
        else
          # Add this match
          {[match | acc_matches], MapSet.put(used_a_ids, a_id), MapSet.put(used_b_ids, b_id)}
        end
      end)

    Enum.reverse(final_matches)
  end

  # Create match records in database and update transaction statuses
  defp create_matches_and_update_transactions(matches, reconciliation_run_id) do
    Repo.transaction(fn ->
      Enum.each(matches, fn match ->
        # Create transaction match record
        match_attrs = %{
          reconciliation_run_id: reconciliation_run_id,
          transaction_a_id: match.transaction_a.id,
          transaction_b_id: match.transaction_b.id,
          match_type: match.match_type,
          confidence_score: match.confidence_score,
          amount_difference: TransactionMatch.calculate_amount_difference(
            match.transaction_a,
            match.transaction_b
          ),
          date_difference_days: TransactionMatch.calculate_date_difference(
            match.transaction_a,
            match.transaction_b
          ),
          matching_criteria: match.matching_criteria
        }

        case Reconciliation.create_transaction_match(match_attrs) do
          {:ok, _transaction_match} ->
            # Update both transactions as matched
            Reconciliation.update_transaction_match_status(match.transaction_a, %{
              is_matched: true,
              match_confidence: match.confidence_score
            })

            Reconciliation.update_transaction_match_status(match.transaction_b, %{
              is_matched: true,
              match_confidence: match.confidence_score
            })

          {:error, changeset} ->
            Logger.error("Failed to create transaction match: #{inspect(changeset.errors)}")
        end
      end)
    end)
  end

  @doc """
  Manually creates a match between two transactions.
  """
  def create_manual_match(transaction_a_id, transaction_b_id, reconciliation_run_id, notes \\ nil) do
    # Get transactions
    transaction_a = Repo.get!(Transaction, transaction_a_id)
    transaction_b = Repo.get!(Transaction, transaction_b_id)

    # Verify they belong to different files
    if transaction_a.uploaded_file.file_type == transaction_b.uploaded_file.file_type do
      {:error, "Cannot match transactions from the same file"}
    else
      Repo.transaction(fn ->
        # Create manual match
        match_changeset = TransactionMatch.create_manual_match(
          transaction_a,
          transaction_b,
          reconciliation_run_id,
          notes
        )

        case Repo.insert(match_changeset) do
          {:ok, match} ->
            # Update transaction statuses
            Reconciliation.update_transaction_match_status(transaction_a, %{
              is_matched: true,
              match_confidence: Decimal.new("100")
            })

            Reconciliation.update_transaction_match_status(transaction_b, %{
              is_matched: true,
              match_confidence: Decimal.new("100")
            })

            # Recalculate reconciliation stats
            Reconciliation.calculate_reconciliation_stats(reconciliation_run_id)

            {:ok, match}

          {:error, changeset} ->
            {:error, changeset}
        end
      end)
    end
  end

  @doc """
  Removes a match between two transactions.
  """
  def remove_match(match_id) do
    match = Repo.get!(TransactionMatch, match_id)
    |> Repo.preload([:transaction_a, :transaction_b])

    Repo.transaction(fn ->
      # Update transaction statuses
      Reconciliation.update_transaction_match_status(match.transaction_a, %{
        is_matched: false,
        match_confidence: nil
      })

      Reconciliation.update_transaction_match_status(match.transaction_b, %{
        is_matched: false,
        match_confidence: nil
      })

      # Delete the match
      Repo.delete!(match)

      # Recalculate reconciliation stats
      Reconciliation.calculate_reconciliation_stats(match.reconciliation_run_id)
    end)
  end
end
