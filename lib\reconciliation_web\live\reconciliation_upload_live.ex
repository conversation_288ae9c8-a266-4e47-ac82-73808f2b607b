defmodule ReconciliationWeb.ReconciliationUploadLive do
  use ReconciliationWeb, :live_view
  import Phoenix.Component

  alias Phoenix.LiveView.UploadEntry
  alias Reconciliation.{ReconciliationRun, UploadedFile}
  alias Reconciliation.Services.{ExcelParser, MatchingEngine}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Create a new reconciliation run
    {:ok, run} = Reconciliation.create_reconciliation_run(%{
      name: "Reconciliation #{DateTime.utc_now() |> DateTime.to_date()}",
      user_id: user.id,
      status: "pending"
    })

    {:ok,
     socket
     |> assign(:page_title, "Upload Reconciliation Files")
     |> assign(:reconciliation_run, run)
     |> assign(:file_a_uploaded, nil)
     |> assign(:file_b_uploaded, nil)
     |> assign(:processing_status, %{file_a: nil, file_b: nil})
     |> assign(:form, to_form(%{"name" => run.name}))
     |> allow_upload(:file_a,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true
        )
     |> allow_upload(:file_b,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true
        )
    }
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_name", %{"name" => name}, socket) do
    {:ok, run} = Reconciliation.update_reconciliation_run(socket.assigns.reconciliation_run, %{name: name})

    {:noreply,
     socket
     |> assign(:reconciliation_run, run)
     |> assign(:form, to_form(%{"name" => name}))
    }
  end

  @impl true
  def handle_event("save", _params, socket) do
    # Process file uploads
    file_a_results = consume_uploaded_entries(socket, :file_a, &handle_file_upload(&1, &2, "file_a", socket))
    file_b_results = consume_uploaded_entries(socket, :file_b, &handle_file_upload(&1, &2, "file_b", socket))

    socket =
      socket
      |> update_file_status(:file_a, file_a_results)
      |> update_file_status(:file_b, file_b_results)

    # Check if both files are uploaded
    if socket.assigns.file_a_uploaded && socket.assigns.file_b_uploaded do
      # Start processing files
      Task.start(fn -> process_reconciliation(socket.assigns.reconciliation_run.id) end)

      socket = put_flash(socket, :info, "Files uploaded successfully! Processing reconciliation...")
      {:noreply, push_navigate(socket, to: ~p"/reconciliation/#{socket.assigns.reconciliation_run.id}/results")}
    else
      {:noreply, socket}
    end
  end

  # Handle upload cancellations
  @impl true
  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_a"}, socket) do
    {:noreply, cancel_upload(socket, :file_a, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_b"}, socket) do
    {:noreply, cancel_upload(socket, :file_b, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    # Fallback for old format
    socket =
      socket
      |> cancel_upload(:file_a, ref)
      |> cancel_upload(:file_b, ref)
    {:noreply, socket}
  end

  # Handle file upload and create database record
  defp handle_file_upload(%{path: path} = meta, entry, file_type, socket) do
    # Create uploaded file record
    file_attrs = %{
      reconciliation_run_id: socket.assigns.reconciliation_run.id,
      file_type: file_type,
      filename: generate_filename(entry.client_name),
      original_filename: entry.client_name,
      file_size: File.stat!(path).size,
      mime_type: entry.client_type,
      file_path: path,
      status: "uploaded"
    }

    case Reconciliation.create_uploaded_file(file_attrs) do
      {:ok, uploaded_file} ->
        {:ok, uploaded_file}
      {:error, _changeset} ->
        {:error, "Failed to save file information"}
    end
  end

  # Update file status in socket assigns
  defp update_file_status(socket, file_type, results) do
    case results do
      [{:ok, uploaded_file}] ->
        assign(socket, String.to_atom("#{file_type}_uploaded"), uploaded_file)
      _ ->
        socket
    end
  end

  # Generate unique filename
  defp generate_filename(original_name) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    extension = Path.extname(original_name)
    base_name = Path.basename(original_name, extension)
    "#{base_name}_#{timestamp}#{extension}"
  end

  # Process reconciliation in background
  defp process_reconciliation(reconciliation_run_id) do
    run = Reconciliation.get_reconciliation_run!(reconciliation_run_id)
    uploaded_files = Reconciliation.get_uploaded_files(reconciliation_run_id)

    try do
      # Update status to processing
      Reconciliation.update_reconciliation_run(run, %{status: "processing"})

      # Parse both files
      Enum.each(uploaded_files, fn file ->
        ExcelParser.parse_file(file)
      end)

      # Perform transaction matching
      MatchingEngine.match_transactions(run)

    rescue
      error ->
        Reconciliation.mark_reconciliation_failed(run, Exception.message(error))
    end
  end

  defp error_to_string(:too_large), do: "File too large (max 50MB)"
  defp error_to_string(:too_many_files), do: "Too many files"
  defp error_to_string(:not_accepted), do: "File type not accepted (.xlsx, .xls, .csv only)"
  defp error_to_string(_), do: "Unknown error"
end
