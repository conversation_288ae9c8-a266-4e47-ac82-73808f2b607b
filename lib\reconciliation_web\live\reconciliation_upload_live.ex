defmodule ReconciliationWeb.ReconciliationUploadLive do
  use ReconciliationWeb, :live_view
  import Phoenix.Component # Added for diagnostics

  alias Phoenix.LiveView.UploadEntry

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "Upload Reconciliation File")
     |> assign(:uploaded_files, [])
     |> assign(:form, Phoenix.HTML.FormData.to_form(%{}))
     |> allow_upload(:excel_file,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 10_000_000, # 10MB, adjust as needed
          auto_upload: true
        )
    }
  end

  # The render/1 function is intentionally removed.
  # Phoenix LiveView will automatically use reconciliation_upload_live.html.heex

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("save", _params, socket) do
    uploaded_files =
      consume_uploaded_entries(socket, :excel_file, fn %{path: path}, _entry ->
        # For now, we'''ll just store the path and a success status.
        # In a real application, you would parse the Excel file here.
        # For example, using a library like Elixir-LS/xlsxir or similar.
        {:ok, path}
      end)

    # Temporary: Put a flash message and clear uploads for now
    # In a real app, you'''d process these files, store data, etc.
    if Enum.any?(uploaded_files) do
      file_paths = Enum.map(uploaded_files, fn {:ok, path} -> path end)
      socket = put_flash(socket, :info, "Files uploaded successfully: #{inspect(file_paths)}")
      {:noreply, assign(socket, uploaded_files: uploaded_files, uploads: %{excel_file: %{entries: []}})}
    else
      {:noreply, socket}
    end
  end

  # Handle upload errors or cancellations if needed
  @impl true
  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    {:noreply, Phoenix.LiveView.cancel_upload(socket, :excel_file, ref)}
  end

  defp error_to_string(:too_large), do: "File too large"
  defp error_to_string(:too_many_files), do: "Too many files"
  defp error_to_string(:not_accepted), do: "File type not accepted"
  defp error_to_string(_), do: "Unknown error"
end