defmodule ReconciliationWeb.DashboardLive do
  use ReconciliationWeb, :live_view

  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Financial Overview Dashboard</h1>
        <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium flex items-center">
          <.icon name="hero-plus" class="w-5 h-5 mr-2" />
          New Reconciliation
        </.link>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Total Transactions</h2>
          <p class="text-3xl font-bold text-blue-600">1,234,567</p>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Matching Rate</h2>
          <p class="text-3xl font-bold text-green-600">98.5%</p>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Discrepancies Found</h2>
          <p class="text-3xl font-bold text-red-600">1,234</p>
        </div>
      </div>

      <!-- Recent Activity / Overview -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-800">Recent Reconciliation Runs</h2>
          <.link navigate={~p"/reconciliation"} class="text-indigo-600 hover:text-indigo-800 font-medium">
            View All →
          </.link>
        </div>

        <%= if @recent_runs && Enum.any?(@recent_runs) do %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match Rate</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for run <- @recent_runs do %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= run.name %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if run.processed_at do %>
                        <%= Calendar.strftime(run.processed_at, "%b %d, %Y") %>
                      <% else %>
                        <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={[
                        "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                        case run.status do
                          "completed" -> "bg-green-100 text-green-800"
                          "processing" -> "bg-yellow-100 text-yellow-800"
                          "failed" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= String.capitalize(run.status) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if run.match_rate do %>
                        <%= Decimal.to_string(run.match_rate, :normal) %>%
                      <% else %>
                        -
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= if run.status == "completed" do %>
                        <.link navigate={~p"/reconciliation/#{run.id}/results"} class="text-indigo-600 hover:text-indigo-900">
                          View Results
                        </.link>
                      <% else %>
                        <span class="text-gray-400">Processing...</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="text-center py-8">
            <.icon name="hero-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 mb-4">No reconciliation runs yet</p>
            <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium">
              Start Your First Reconciliation
            </.link>
          </div>
        <% end %>
      </div>

      <!-- Charts Placeholder -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Matching Trends</h2>
          <!-- Placeholder for a chart component -->
          <div class="bg-gray-200 h-64 flex items-center justify-center text-gray-500">
            [Chart Placeholder]
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Discrepancy Categories</h2>
          <!-- Placeholder for another chart component -->
          <div class="bg-gray-200 h-64 flex items-center justify-center text-gray-500">
            [Chart Placeholder]
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Get recent reconciliation runs for the user
    recent_runs = Reconciliation.list_reconciliation_runs(user.id)
    |> Enum.take(5)  # Show only the 5 most recent

    {:ok, assign(socket, recent_runs: recent_runs)}
  end
end
