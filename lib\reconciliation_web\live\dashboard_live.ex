defmodule ReconciliationWeb.DashboardLive do
  use ReconciliationWeb, :live_view

  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-6">Financial Overview Dashboard</h1>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Total Transactions</h2>
          <p class="text-3xl font-bold text-blue-600">1,234,567</p>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Matching Rate</h2>
          <p class="text-3xl font-bold text-green-600">98.5%</p>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-600 mb-2">Discrepancies Found</h2>
          <p class="text-3xl font-bold text-red-600">1,234</p>
        </div>
      </div>

      <!-- Recent Activity / Overview -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Recent Reconciliation Runs</h2>
        <!-- Placeholder for a table or list of recent runs -->
        <div class="text-gray-600">
          <p>Display a summary table or list of the most recent reconciliation processes here.</p>
          <p>e.g., Run ID, Date, Status, Matched, Discrepancies</p>
        </div>
      </div>

      <!-- Charts Placeholder -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Matching Trends</h2>
          <!-- Placeholder for a chart component -->
          <div class="bg-gray-200 h-64 flex items-center justify-center text-gray-500">
            [Chart Placeholder]
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Discrepancy Categories</h2>
          <!-- Placeholder for another chart component -->
          <div class="bg-gray-200 h-64 flex items-center justify-center text-gray-500">
            [Chart Placeholder]
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, socket}
  end
end
