defmodule Reconciliation.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      ReconciliationWeb.Telemetry,
      Reconciliation.Repo,
      {DNSCluster, query: Application.get_env(:reconciliation, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Reconciliation.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: Reconciliation.Finch},
      # Start a worker by calling: Reconciliation.Worker.start_link(arg)
      # {Reconciliation.Worker, arg},
      # Start to serve requests, typically the last entry
      ReconciliationWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Reconciliation.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    ReconciliationWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
