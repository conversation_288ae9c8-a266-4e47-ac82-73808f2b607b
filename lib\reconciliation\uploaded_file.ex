defmodule Reconciliation.UploadedFile do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.{ReconciliationRun, Transaction}

  schema "uploaded_files" do
    field :file_type, :string
    field :filename, :string
    field :original_filename, :string
    field :file_size, :integer
    field :mime_type, :string
    field :file_path, :string
    field :status, :string, default: "uploaded"
    field :total_rows, :integer, default: 0
    field :processed_rows, :integer, default: 0
    field :error_rows, :integer, default: 0
    field :headers_detected, {:array, :string}
    field :column_mapping, :map
    field :processing_errors, {:array, :string}
    field :processed_at, :utc_datetime

    belongs_to :reconciliation_run, ReconciliationRun
    has_many :transactions, Transaction, on_delete: :delete_all

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :file_type, :filename, :original_filename, :file_size, :mime_type,
      :file_path, :status, :total_rows, :processed_rows, :error_rows,
      :headers_detected, :column_mapping, :processing_errors, :processed_at,
      :reconciliation_run_id
    ])
    |> validate_required([:file_type, :filename, :original_filename, :status, :reconciliation_run_id])
    |> validate_inclusion(:file_type, ["file_a", "file_b"])
    |> validate_inclusion(:status, ["uploaded", "processing", "processed", "failed"])
    |> validate_length(:filename, min: 1, max: 255)
    |> validate_length(:original_filename, min: 1, max: 255)
    |> validate_number(:file_size, greater_than: 0)
    |> validate_number(:total_rows, greater_than_or_equal_to: 0)
    |> validate_number(:processed_rows, greater_than_or_equal_to: 0)
    |> validate_number(:error_rows, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:reconciliation_run_id)
  end

  @doc """
  Creates a changeset for updating file processing status
  """
  def processing_changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :status, :total_rows, :processed_rows, :error_rows,
      :headers_detected, :column_mapping, :processing_errors, :processed_at
    ])
    |> validate_required([:status])
    |> validate_inclusion(:status, ["uploaded", "processing", "processed", "failed"])
    |> validate_number(:total_rows, greater_than_or_equal_to: 0)
    |> validate_number(:processed_rows, greater_than_or_equal_to: 0)
    |> validate_number(:error_rows, greater_than_or_equal_to: 0)
  end

  @doc """
  Creates a changeset for marking file processing as failed
  """
  def error_changeset(uploaded_file, errors) do
    uploaded_file
    |> cast(%{status: "failed", processing_errors: errors}, [:status, :processing_errors])
    |> validate_required([:status, :processing_errors])
  end

  @doc """
  Checks if the file has been processed successfully
  """
  def processed?(%__MODULE__{status: "processed"}), do: true
  def processed?(_), do: false

  @doc """
  Checks if the file is currently being processed
  """
  def processing?(%__MODULE__{status: "processing"}), do: true
  def processing?(_), do: false

  @doc """
  Checks if the file processing has failed
  """
  def failed?(%__MODULE__{status: "failed"}), do: true
  def failed?(_), do: false

  @doc """
  Calculates processing progress percentage
  """
  def progress_percentage(%__MODULE__{total_rows: total, processed_rows: processed})
      when total > 0 do
    (processed / total * 100) |> Float.round(1)
  end

  def progress_percentage(_), do: 0.0

  @doc """
  Returns the file extension from filename
  """
  def file_extension(%__MODULE__{filename: filename}) do
    filename
    |> Path.extname()
    |> String.downcase()
  end

  @doc """
  Checks if the file is an Excel file
  """
  def excel_file?(%__MODULE__{} = file) do
    file_extension(file) in [".xlsx", ".xls"]
  end

  @doc """
  Checks if the file is a CSV file
  """
  def csv_file?(%__MODULE__{} = file) do
    file_extension(file) == ".csv"
  end

  @doc """
  Returns human-readable file size
  """
  def human_file_size(%__MODULE__{file_size: nil}), do: "Unknown"
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024, do: "#{size} B"
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024 * 1024 do
    "#{Float.round(size / 1024, 1)} KB"
  end
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024 * 1024 * 1024 do
    "#{Float.round(size / (1024 * 1024), 1)} MB"
  end
  def human_file_size(%__MODULE__{file_size: size}) do
    "#{Float.round(size / (1024 * 1024 * 1024), 1)} GB"
  end
end
