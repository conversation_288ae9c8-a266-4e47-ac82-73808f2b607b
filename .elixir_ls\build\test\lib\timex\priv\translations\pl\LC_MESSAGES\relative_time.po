# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: pl\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON>mi<PERSON><PERSON><PERSON>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7.1\n"

#: lib/l10n/translator.ex:320
msgid "last month"
msgstr "zeszły miesiąc"

#: lib/l10n/translator.ex:326
msgid "last week"
msgstr "zeszły tydzień"

#: lib/l10n/translator.ex:314
msgid "last year"
msgstr "zeszły rok"

#: lib/l10n/translator.ex:322
msgid "next month"
msgstr "przyszły miesiąc"

#: lib/l10n/translator.ex:328
msgid "next week"
msgstr "przyszły tydzień"

#: lib/l10n/translator.ex:316
msgid "next year"
msgstr "przyszły rok"

#: lib/l10n/translator.ex:321
msgid "this month"
msgstr "ten miesiąc"

#: lib/l10n/translator.ex:327
msgid "this week"
msgstr "ten tydzień"

#: lib/l10n/translator.ex:315
msgid "this year"
msgstr "ten rok"

#: lib/l10n/translator.ex:333
msgid "today"
msgstr "dziś"

#: lib/l10n/translator.ex:334
msgid "tomorrow"
msgstr "jutro"

#: lib/l10n/translator.ex:332
msgid "yesterday"
msgstr "wczoraj"

#: lib/l10n/translator.ex:336
msgid "%{count} day ago"
msgid_plural "%{count} days ago"
msgstr[0] "%{count} dzień temu"
msgstr[1] "%{count} dni temu"
msgstr[2] "%{count} dni temu"

#: lib/l10n/translator.ex:361
msgid "%{count} hour ago"
msgid_plural "%{count} hours ago"
msgstr[0] "%{count} godzinę temu"
msgstr[1] "%{count} godziny temu"
msgstr[2] "%{count} godzin temu"

#: lib/l10n/translator.ex:364
msgid "%{count} minute ago"
msgid_plural "%{count} minutes ago"
msgstr[0] "%{count} minutę temu"
msgstr[1] "%{count} minuty temu"
msgstr[2] "%{count} minut temu"

#: lib/l10n/translator.ex:324
msgid "%{count} month ago"
msgid_plural "%{count} months ago"
msgstr[0] "%{count} miesiąc temu"
msgstr[1] "%{count} miesiące temu"
msgstr[2] "%{count} miesięcy temu"

#: lib/l10n/translator.ex:367
msgid "%{count} second ago"
msgid_plural "%{count} seconds ago"
msgstr[0] "%{count} sekunda temu"
msgstr[1] "%{count} sekundy temu"
msgstr[2] "%{count} sekund temu"

#: lib/l10n/translator.ex:330
msgid "%{count} week ago"
msgid_plural "%{count} weeks ago"
msgstr[0] "%{count} tydzień temu"
msgstr[1] "%{count} tygodnie temu"
msgstr[2] "%{count} tygodni temu"

#: lib/l10n/translator.ex:318
msgid "%{count} year ago"
msgid_plural "%{count} years ago"
msgstr[0] "%{count} rok temu"
msgstr[1] "%{count} lata temu"
msgstr[2] "%{count} lat temu"

#: lib/l10n/translator.ex:335
msgid "in %{count} day"
msgid_plural "in %{count} days"
msgstr[0] "za %{count} dzień"
msgstr[1] "za %{count} dni"
msgstr[2] "za %{count} dni"

#: lib/l10n/translator.ex:360
msgid "in %{count} hour"
msgid_plural "in %{count} hours"
msgstr[0] "za %{count} godzinę"
msgstr[1] "za %{count} godziny"
msgstr[2] "za %{count} godzin"

#: lib/l10n/translator.ex:363
msgid "in %{count} minute"
msgid_plural "in %{count} minutes"
msgstr[0] "za %{count} minutę"
msgstr[1] "za %{count} minuty"
msgstr[2] "za %{count} minut"

#: lib/l10n/translator.ex:323
msgid "in %{count} month"
msgid_plural "in %{count} months"
msgstr[0] "za %{count} miesiąc"
msgstr[1] "za %{count} miesiące"
msgstr[2] "za %{count} miesięcy"

#: lib/l10n/translator.ex:366
msgid "in %{count} second"
msgid_plural "in %{count} seconds"
msgstr[0] "za %{count} sekundę"
msgstr[1] "za %{count} sekundy"
msgstr[2] "za %{count} sekund"

#: lib/l10n/translator.ex:329
msgid "in %{count} week"
msgid_plural "in %{count} weeks"
msgstr[0] "za %{count} tydzień"
msgstr[1] "za %{count} tygodnie"
msgstr[2] "za %{count} tygodni"

#: lib/l10n/translator.ex:317
msgid "in %{count} year"
msgid_plural "in %{count} years"
msgstr[0] "za %{count} rok"
msgstr[1] "za %{count} lata"
msgstr[2] "za %{count} lat"

#: lib/l10n/translator.ex:350
msgid "last friday"
msgstr "zeszły piątek"

#: lib/l10n/translator.ex:338
msgid "last monday"
msgstr "zeszły poniedziałek"

#: lib/l10n/translator.ex:353
msgid "last saturday"
msgstr "zeszła sobota"

#: lib/l10n/translator.ex:356
msgid "last sunday"
msgstr "zeszła niedziela"

#: lib/l10n/translator.ex:347
msgid "last thursday"
msgstr "zeszły czwartek"

#: lib/l10n/translator.ex:341
msgid "last tuesday"
msgstr "zeszły wtorek"

#: lib/l10n/translator.ex:344
msgid "last wednesday"
msgstr "zeszła środa"

#: lib/l10n/translator.ex:352
msgid "next friday"
msgstr "przyszły piątek"

#: lib/l10n/translator.ex:340
msgid "next monday"
msgstr "przyszły poniedziałek"

#: lib/l10n/translator.ex:355
msgid "next saturday"
msgstr "przyszła sobota"

#: lib/l10n/translator.ex:358
msgid "next sunday"
msgstr "przyszła niedziela"

#: lib/l10n/translator.ex:349
msgid "next thursday"
msgstr "przyszły czwartek"

#: lib/l10n/translator.ex:343
msgid "next tuesday"
msgstr "przyszły wtorek"

#: lib/l10n/translator.ex:346
msgid "next wednesday"
msgstr "przyszła środa"

#: lib/l10n/translator.ex:351
msgid "this friday"
msgstr "ten piątek"

#: lib/l10n/translator.ex:339
msgid "this monday"
msgstr "ten poniedziałek"

#: lib/l10n/translator.ex:354
msgid "this saturday"
msgstr "ta sobota"

#: lib/l10n/translator.ex:357
msgid "this sunday"
msgstr "ta niedziela"

#: lib/l10n/translator.ex:348
msgid "this thursday"
msgstr "ten czwartek"

#: lib/l10n/translator.ex:342
msgid "this tuesday"
msgstr "ten wtorek"

#: lib/l10n/translator.ex:345
msgid "this wednesday"
msgstr "ta środa"

#: lib/l10n/translator.ex:369
msgid "now"
msgstr "teraz"
