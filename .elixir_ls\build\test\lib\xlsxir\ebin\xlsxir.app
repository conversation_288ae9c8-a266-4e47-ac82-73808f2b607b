{application,xlsxir,
             [{applications,[kernel,stdlib,elixir,logger,erlsom]},
              {description,"Xlsx file parser (Excel, LibreOffice, etc.)\n"},
              {modules,['Elixir.Xlsxir','Elixir.Xlsxir.ConvertDate',
                        'Elixir.Xlsxir.ConvertDateTime',
                        'Elixir.Xlsxir.ParseString',
                        'Elixir.Xlsxir.ParseStyle',
                        'Elixir.Xlsxir.ParseWorkbook',
                        'Elixir.Xlsxir.ParseWorksheet',
                        'Elixir.Xlsxir.SaxError','Elixir.Xlsxir.SaxParser',
                        'Elixir.Xlsxir.StateManager',
                        'Elixir.Xlsxir.StreamWorksheet','Elixir.Xlsxir.Unzip',
                        'Elixir.Xlsxir.XlsxFile','Elixir.Xlsxir.XmlFile']},
              {registered,[]},
              {vsn,"1.6.4"},
              {mod,{'Elixir.Xlsxir',[]}}]}.
