# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: en\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=iso-8859-1\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "Fri"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "Friday"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "Mon"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "Monday"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "Sat"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "Saturday"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "Sun"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "Sunday"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "Thu"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "Thursday"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "Tue"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "Tuesday"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "Wed"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "Wednesday"
