{application,tzdata,
             [{applications,[kernel,stdlib,elixir,logger,hackney]},
              {description,"<PERSON><PERSON><PERSON> is a parser and library for the tz database.\n"},
              {modules,['Elixir.Tzdata','Elixir.Tzdata.App',
                        'Elixir.Tzdata.BasicDataMap',
                        'Elixir.Tzdata.DataBuilder',
                        'Elixir.Tzdata.DataLoader','Elixir.Tzdata.EtsHolder',
                        'Elixir.Tzdata.FarFutureDynamicPeriods',
                        'Elixir.Tzdata.HTTPClient',
                        'Elixir.Tzdata.HTTPClient.Hackney',
                        'Elixir.Tzdata.LeapSecParser','Elixir.Tzdata.Parser',
                        'Elixir.Tzdata.ParserOrganizer',
                        'Elixir.Tzdata.PeriodBuilder',
                        'Elixir.Tzdata.ReleaseReader',
                        'Elixir.Tzdata.ReleaseUpdater',
                        'Elixir.Tzdata.TimeZoneDatabase',
                        'Elixir.Tzdata.Util']},
              {registered,[]},
              {vsn,"1.1.3"},
              {env,[{autoupdate,enabled},
                    {data_dir,nil},
                    {http_client,'Elixir.Tzdata.HTTPClient.Hackney'}]},
              {mod,{'Elixir.Tzdata.App',[]}}]}.
