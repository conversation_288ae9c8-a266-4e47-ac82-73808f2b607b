<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="employee" type="extendedpersoninfo"/>
  <xs:complexType name="personinfo">
    <xs:attribute name="id" type="xs:positiveInteger"/>
  </xs:complexType>
  <xs:complexType name="extendedpersoninfo">
    <xs:complexContent>
      <xs:extension base="personinfo">
        <xs:all>
          <xs:element name="firstname" type="xs:string"/>
          <xs:element name="lastname" type="xs:string"/>
          <xs:element name="address" type="xs:string"/>
          <xs:element name="city" type="xs:string"/>
          <xs:element name="country" type="xs:string"/>
        </xs:all>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>

