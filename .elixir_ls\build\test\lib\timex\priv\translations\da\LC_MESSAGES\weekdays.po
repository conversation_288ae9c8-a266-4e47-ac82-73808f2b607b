# # `msgid`s in this file come from POT (.pot) files.
# #
# # Do not add, change, or remove `msgid`s manually here as
# # they're tied to the ones in the corresponding POT file
# # (with the same domain).
# #
# # Use `mix gettext.extract --merge` or `mix gettext.merge`
# # to merge POT files into PO files.
msgid ""
msgstr ""
"Language: da\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.11\n"

#: lib/l10n/translator.ex:275
msgid "Fri"
msgstr "Fre"

#: lib/l10n/translator.ex:283
msgid "Friday"
msgstr "Fredag"

#: lib/l10n/translator.ex:271
msgid "Mon"
msgstr "Man"

#: lib/l10n/translator.ex:279
msgid "Monday"
msgstr "Mandag"

#: lib/l10n/translator.ex:276
msgid "Sat"
msgstr "Lør"

#: lib/l10n/translator.ex:284
msgid "Saturday"
msgstr "Lørdag"

#: lib/l10n/translator.ex:277
msgid "Sun"
msgstr "Søn"

#: lib/l10n/translator.ex:285
msgid "Sunday"
msgstr "Søndag"

#: lib/l10n/translator.ex:274
msgid "Thu"
msgstr "Tor"

#: lib/l10n/translator.ex:282
msgid "Thursday"
msgstr "Torsdag"

#: lib/l10n/translator.ex:272
msgid "Tue"
msgstr "Tir"

#: lib/l10n/translator.ex:280
msgid "Tuesday"
msgstr "Tirsdag"

#: lib/l10n/translator.ex:273
msgid "Wed"
msgstr "Ons"

#: lib/l10n/translator.ex:281
msgid "Wednesday"
msgstr "Onsdag"
